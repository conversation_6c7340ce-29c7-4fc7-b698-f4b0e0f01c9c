import {Services} from "resource://gre/modules/Services.sys.mjs";

/**
 * Module to handle migration of old preferences to new formats
 * Runs during browser startup or when importing saved preference files
 */
export const PreferenceMigrator = {
  /**
   * Mapping of old tab context menu preferences to DOM element IDs
   */
  TAB_CONTEXT_MAPPING: [
    ["newTabMenu", "context_openANewTab"],
    ["duplicateMenu", "context_duplicateTab"],
    ["duplicateinWinMenu", "tm-duplicateinWin"],
    ["detachTabMenu", "context_openTabInWindow"],
    ["muteTabMenu", "context_toggleMuteTab"],
    ["pinTabMenu", "context_pinTab"],
    ["moveTabOptions", "context_moveTabOptions"],
    ["sendTabToDevice", "context_sendTabToDevice"],
    ["shareTabURL", "share-tab-url-item"],
    ["showMergeWindow", "tm-mergeWindowsTab"],
    ["renameTabMenu", "tm-renameTab"],
    ["copyTabUrlMenu", "tm-copyTabUrl"],
    ["reopenInContainer", "context_reopenInContainer"],
    ["selectAllTabs", "context_selectAllTabs"],
    ["reloadTabMenu", "context_reloadTab"],
    ["autoReloadMenu", "tm-autoreloadTab_menu"],
    ["reloadTabOptions", "context_reloadTabOptions"],
    ["undoCloseTabMenu", "context_undoCloseTab"],
    ["undoCloseListMenu", "tm-undoCloseList"],
    ["closeTabMenu", "context_closeTab"],
    ["closeDuplicateTabs", "context_closeDuplicateTabs"],
    ["closeTabOptions", "context_closeTabOptions"],
    ["docShellMenu", "tm-docShell"],
    ["freezeTabMenu", "tm-freezeTab"],
    ["protectTabMenu", "tm-protectTab"],
    ["lockTabMenu", "tm-lockTab"],
    ["bookmarkTabMenu", "context_bookmarkTab"],
    ["bookmarkAllTabsMenu", "context_bookmarkAllTabs"]
  ],

  /**
   * Convert all old boolean tab context menu preferences to a single JSON string preference
   * 
   * @returns {string} JSON string containing IDs of menu items that should be hidden
   */
  migrateTabContextPrefs() {
    const tabmixPrefs = Services.prefs.getBranch("extensions.tabmix.");
    const hiddenItems = [];

    // Check each old preference and add its ID to hiddenItems if false
    for (const [pref, id] of this.TAB_CONTEXT_MAPPING) {
      try {
        if (tabmixPrefs.prefHasUserValue(pref) && !tabmixPrefs.getBoolPref(pref)) {
          hiddenItems.push(id);
        }
      } catch (ex) {
        console.error(`Error reading preference ${pref}:`, ex);
      }
    }

    // Convert hiddenItems to JSON string and set the new preference
    const jsonString = JSON.stringify(hiddenItems);
    tabmixPrefs.setCharPref("tabContextMenu", jsonString);
    
    // Clear old preferences
    for (const [pref] of this.TAB_CONTEXT_MAPPING) {
      try {
        if (tabmixPrefs.prefHasUserValue(pref)) {
          tabmixPrefs.clearUserPref(pref);
        }
      } catch (ex) {
        console.error(`Error clearing preference ${pref}:`, ex);
      }
    }

    return jsonString;
  },

  /**
   * Check if old tab context menu preferences exist and migrate them if needed
   * 
   * @returns {boolean} True if migration was performed
   */
  tabContextMigrationNeeded() {
    const tabmixPrefs = Services.prefs.getBranch("extensions.tabmix.");
    
    // Check if any old preferences exist
    return this.TAB_CONTEXT_MAPPING.some(([pref]) => {
      try {
        return tabmixPrefs.prefHasUserValue(pref);
      } catch (ex) {
        return false;
      }
    });
  },

  /**
   * Run all preference migrations as needed
   */
  runAllMigrations() {
    if (this.tabContextMigrationNeeded()) {
      this.migrateTabContextPrefs();
    }
    
    // Add other migrations here as needed
    // this.migrateOtherPrefs();
  }
};