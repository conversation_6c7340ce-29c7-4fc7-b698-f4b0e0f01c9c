{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Debug ESLint Rule", "program": "${workspaceFolder}/node_modules/eslint/bin/eslint.js", "args": ["--config", "${workspaceFolder}/eslint.config.js", "--debug", "${file}"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"type": "node", "request": "launch", "name": "Debug ESLint Rule with <PERSON> Scrip<PERSON>", "program": "${workspaceFolder}/config/eslint-plugin-tabmix/tests/debug-rule.js", "args": ["${input:testFile}"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"type": "node", "request": "attach", "name": "Attach to VS Code ESLint Server", "port": 6009, "restart": true, "outFiles": ["${workspaceFolder}/node_modules/eslint/lib/**/*.js", "${workspaceFolder}/config/eslint-plugin-tabmix/**/*.js"]}, {"type": "node", "request": "launch", "name": "Test VS Code Integration", "program": "${workspaceFolder}/config/eslint-plugin-tabmix/test-vscode-integration.js", "args": ["${input:testFile}"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}], "inputs": [{"id": "testFile", "type": "promptString", "description": "Enter the path to the file you want to test", "default": "addon/chrome/content/tabmix.js"}]}