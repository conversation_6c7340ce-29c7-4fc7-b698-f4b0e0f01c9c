/* ===== preferences.css =====================================================
  == Styles used by the XUL prefwindow element.
  ======================================================================= */

  @namespace html url("http://www.w3.org/1999/xhtml");

  /* ::::: dialog ::::: */

  prefwindow {
    padding: 0;
  }

  prefpane {
    padding-top: 8px;
    padding-bottom: 10px;
    padding-inline: 8px 10px;
  }

  prefwindow[type="child"] {
    padding-top: 8px;
    padding-bottom: 10px;
    padding-inline: 8px 10px;
  }

  prefwindow[type="child"] > prefpane {
    padding: 0;
  }

  .prefWindow-dlgbuttons {
    padding-bottom: 10px;
    padding-inline: 8px 10px;
  }

  prefwindow[type="child"] .prefWindow-dlgbuttons {
    padding: 0;
  }

  radio[pane] {
    appearance: none;
    margin: 0 1px;
    padding: 1px 3px;
    min-width: 4.5em;
  }

  .paneSelector {
    border-bottom: 2px groove ThreeDFace;
    margin: 0;
    padding-inline-start: 10px;
    background-color: -moz-Field;
    color: -moz-FieldText;
  }

  @media (-moz-platform: linux) {
    prefwindow radiogroup.paneSelector:focus-visible > radio[focused="true"] > .radio-label-box {
      outline: none;
    }
  }

  .paneButtonIcon {
    width: 32px;
    height: 32px;
  }

  radio[pane]:hover {
    background-color: #E0E8F6;
    color: var(--in-content-button-text-color-hover);
    appearance: none;
  }

  radio[pane][selected="true"] {
    background-color: #C1D2EE;
    color: var(--in-content-button-text-color-hover);
    appearance: none;
  }
