/**
    Firefox all versions

    ALL platforms

**/

/* for privateTab extensions */
#TabsToolbar[privateTab-fixAfterTabsButtonsAccessibility]
    #tabbrowser-tabs[tabmix-flowing="multibar"][widthFitTitle="true"] #tabs-newtab-button {
  vertical-align: bottom;
}

/* :::: for tabs close button on TabsToolbar :::: */
#tabmix-tabs-closebutton[tabmix-fill-opacity] {
  appearance: none;
  fill-opacity: 0;
  opacity: 0.8
}

#tabmix-tabs-closebutton.close-icon {
  width: unset;
}

#tabmix-tabs-closebutton:not([overflowedItem]):hover {
  background-color: transparent;
}

#widget-overflow-list #tabmix-tabs-closebutton.close-icon > .toolbarbutton-text {
  display: block;
}

/* :::: Space on tab bar :::: */
#tabbrowser-arrowscrollbox[orient="horizontal"][tabmix-tabBarSpace] {
  -moz-margin-start: 0;
  -moz-margin-end: 0;
  -moz-padding-end: 15px;
  -moz-padding-start: 15px;
}

#tabbrowser-arrowscrollbox[orient="vertical"][tabmix-tabBarSpace] {
  padding-block: 15px;
}

#TabsToolbar[tabmix-tabBarSpace] .titlebar-spacer[type="pre-tabs"],
#TabsToolbar[tabmix-tabBarSpace] .titlebar-spacer[type="post-tabs"] {
  width: 25px;
}

/* same width as history > Recently Closed Windows   */
.tabmix-subview {
  max-width: 32em !important;
}

.tabmix-subview menuseparator.subviewbutton {
  border-top: 1px solid var(--panel-separator-color);
  min-height: 0;
  padding: 0;
}

.context-open {
  outline: var(--focus-outline);
  outline-offset: var(--focus-outline-inset);
}

#tabbrowser-tabs[no-animation] .tabbrowser-tab:not([pinned]) {
	transition: none !important;
}

#tabbrowser-tabs[orient="horizontal"][tabmix-multibar]:not([widthFitTitle]) #tabbrowser-arrowscrollbox > .tabbrowser-tab[fadein]:not([pinned]),
#tabbrowser-tabs[orient="horizontal"][tabmix-multibar]:not([widthFitTitle]) #tabbrowser-arrowscrollbox > tab-group:not([collapsed]) > .tabbrowser-tab[fadein]:not([pinned]) {
  max-width: var(--tabmix-tab-overflow-width, var(--tab-min-width)) !important;
}
