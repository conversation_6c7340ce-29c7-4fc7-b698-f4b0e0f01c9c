<?xml version="1.0"?>

<!DOCTYPE overlay [
<!ENTITY % tabmixDTD SYSTEM "chrome://tabmixplus/locale/tabmix.dtd" >
%tabmixDTD;
]>

<overlay id="tabmix-tabContextMenu-overlay"
   xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul">

  <popupset id="mainPopupSet">
    <menupopup id="tabContextMenu">
        <menuitem id="context_openANewTab"
                 _afterthis="(&afterthis.label;)"/>
        <menu id="tm-undoCloseList"
                 tmp_iconic="menu-iconic closedtabs-icon"
                 label="&undoCloseListMenu.label;" accesskey="&undoCloseListMenu.accesskey;">
          <menupopup id="tm-undoCloseList-menu"/>
        </menu>
        <menuitem id="tm-duplicateinWin"
                 label="&duplicateinWin.label;" accesskey="&duplicateinWin.accesskey;"/>
        <menuitem id="tm-mergeWindowsTab"
                 key="key_tm_mergeWin" label="&mergeContext.label;" accesskey="&mergeContext.accesskey;"/>
        <menuitem id="tm-renameTab"
                 label="&renametab.label;" accesskey="&renametab.accesskey;"/>
        <menuitem id="tm-copyTabUrl"
                 label="&copytaburl.label;" accesskey="&copytaburl.accesskey;"/>
        <menu id="tm-autoreloadTab_menu"
              labelTab="&autoReloadTab.label;" accesskeyTab="&autoReloadTab.accesskey;"
              labelSite="&autoReloadSite.label;" accesskeySite="&autoReloadSite.accesskey;">
           <menupopup data-popup="autoReload"/>
        </menu>
        <menu id="context_reloadTabOptions"
            tabmix=""
            label="Reload Multiple Tabs">
          <menupopup id="reloadTabOptions">
            <!-- add menu item to reload selected tabs -->
            <menuitem id="tm-reloadLeft"
                    label="&reloadleft.label;" accesskey="&reloadleft.accesskey;"/>
            <menuitem id="tm-reloadRight"
                    label="&reloadright.label;" accesskey="&reloadright.accesskey;"/>
            <menuitem id="context_reloadAllTabs"
                    data-lazy-l10n-id="content-blocking-reload-tabs-button"/>
            <menuitem id="tm-reloadOther"
                    label="&reloadother.label;" accesskey="&reloadother.accesskey;"/>
          </menupopup>
        </menu>
        <menuseparator id="tabmix_reloadTabOptions_separator"/>
        <menuitem id="tm-closeAllTabs"
                 label="&closeAllTabsMenu.label;" accesskey="&closeall.accesskey;"/>
        <menuitem id="tm-closeSimilar"
                 label="&closeSimilarTab.label;" accesskey="&closeSimilarTab.accesskey;"/>

        <menuseparator id="tabmix_closeTab_separator"/>
        <menu id="tm-docShell"
                label="&docShellMenu.label;" accesskey="&docShellMenu.accesskey;">
         <menupopup id="tabmix-docShell-popup">
           <menuitem label="&allowImage.label;" value="Images" class="menuitem-iconic" type="checkbox" checked="true" />
           <menuitem label="&allowFrame.label;" value="Subframes" class="menuitem-iconic" type="checkbox" checked="true" />
           <menuitem label="&allowRedirect.label;" value="MetaRedirects" class="menuitem-iconic" type="checkbox" checked="true" />
           <menuitem label="&allowPlugin.label;" value="Plugins" class="menuitem-iconic" type="checkbox" checked="true" />
           <menuitem label="&allowJavascript.label;" value="Javascript" class="menuitem-iconic" type="checkbox" checked="true" />
         </menupopup>
        </menu>
        <menuitem id="tm-freezeTab"
                  label="&freezeTabMenu.label;" accesskey="&freezeTabMenu.accesskey;" type="checkbox"/>
        <menuitem id="tm-protectTab"
                  label="&protectTabMenu.label;" accesskey="&protectTabMenu.accesskey;" type="checkbox"/>
        <menuitem id="tm-lockTab"
                  label="&lockTabMenu.label;" accesskey="&lockTabMenu.accesskey;" type="checkbox"/>
        <menuseparator id="tabmix_lockTab_separator"/>
        <menuitem id="context_bookmarkAllTabs"
                  tabmix=""
                  command="Browser:BookmarkAllTabs"
                  data-lazy-l10n-id="menu-bookmarks-all-tabs"/>

    </menupopup>
  </popupset>

</overlay>
