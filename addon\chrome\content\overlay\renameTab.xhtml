<?xml version="1.0"?>

<?xml-stylesheet href="chrome://tabmixplus/skin/renameTab.css" type="text/css"?>

<!DOCTYPE overlay [
<!ENTITY % miscDTD SYSTEM "chrome://tabmixplus/locale/misc.dtd" >
%miscDTD;
]>

<overlay id="renameTabOverlay"
         xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"
         xmlns:html="http://www.w3.org/1999/xhtml">

  <panel id="tabmixRenametab_panel"
         orient="vertical" type="arrow" hidden="true"
         noautofocus="true" level="top" minwidth="330">
    <row align="center">
      <vbox align="center">
        <image id="tabmixRenametab_icon"/>
      </vbox>
      <vbox>
        <label id="tabmixRenametab_title" value="&renametab.panel.title;"/>
        <hbox>
          <button id="tabmixRenametab_resetButton"
                  label="&renametab.reset.label;"
                  class="editBookmarkPanelHeaderButton"/>
        </hbox>
      </vbox>
     </row>
    <spacer/>
    <vbox flex="1">
      <label control="tabmixRenametab_titleField" value="&title.label;:"/>
      <html:input id="tabmixRenametab_titleField"/>
      <vbox id="tabmixRenametab_defaultRow">
        <label control="tabmixRenametab_defaultField" value="&default.label;:"/>
        <html:input id="tabmixRenametab_defaultField"
                  readonly="true" plain="true"/>
      </vbox>
    </vbox>
    <spacer/>
    <checkbox id="tabmixRenametab_checkbox" label="&renametab.permanently.label;"
              align="center" checked="true"
              tooltiptext="&renametab.permanently.tooltip;"/>
    <spacer/>
    <hbox id="tabmixRenametab_buttons" pack="end">
      <button id="tabmixRenametab_doneButton"
              class="editBookmarkPanelBottomButton"
              default="true"/>
      <button id="tabmixRenametab_deleteButton"
              class="editBookmarkPanelBottomButton"/>
    </hbox>
  </panel>

</overlay>
