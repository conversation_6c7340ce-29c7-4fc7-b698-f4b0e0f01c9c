/* eslint @stylistic/lines-around-comment: "off", prefer-const:"error" */

// Tabmix preference branch
const TabmixPrefs = Services.prefs.getBranch("extensions.tabmix.");

/** @typedef {{firefox: string[]; tabmix: string[]}} ContextMenuHiddenItems */

/**
 * Module to handle migration of old preferences to new formats Runs during
 * browser startup or when importing saved preference files
 */

/**
 * Mapping of old tab context menu preferences to new format Organized by
 * namespace for easier validation and lookup
 *
 * @type {{
 *   firefox: {[id: string]: string};
 *   tabmix: {[id: string]: string};
 *   defaultHidden: ContextMenuHiddenItems;
 * }} }
 */
const TAB_CONTEXT_MAPPING = {
  firefox: {
    bookmarkAllTabs: "bookmarkTabsMenu",
    bookmarkTab: "bookmarkTabMenu",
    closeTab: "closeTabMenu",
    closeTabOptions: "closeTabOptions",
    closeDuplicateTabs: "closeDuplicateTabs",
    openTabInWindow: "detachTabMenu",
    duplicateTab: "duplicateMenu",
    moveTabOptions: "moveTabOptions",
    toggleMuteTab: "muteTabMenu",
    openANewTab: "newTabMenu",
    pinTab: "pinTabMenu",
    reloadTab: "reloadTabMenu",
    reloadTabOptions: "reloadTabOptions",
    reopenInContainer: "reopenInContainer",
    selectAllTabs: "selectAllTabs",
    sendTabToDevice: "sendTabToDevice",
    shareTabURL: "shareTabURL",
    undoCloseTab: "undoCloseTabMenu",
  },
  tabmix: {
    autoreloadTab_menu: "autoReloadMenu",
    copyTabUrl: "copyTabUrlMenu",
    docShell: "docShellMenu",
    duplicateinWin: "duplicateinWinMenu",
    freezeTab: "freezeTabMenu",
    lockTab: "lockTabMenu",
    protectTab: "protectTabMenu",
    renameTab: "renameTabMenu",
    mergeWindowsTab: "showMergeWindow",
    undoCloseList: "undoCloseListMenu",
  },
  defaultHidden: {
    firefox: ["openTabInWindow"],
    tabmix: [
      "autoreloadTab_menu",
      "docShell",
      "duplicateinWin",
      "freezeTab",
      "renameTab",
      "mergeWindowsTab",
    ],
  },
};

export const PreferenceMigrator = {
  /**
   * Validates the tabContextMenu preference and returns the current state
   *
   * @returns {{isFirstRun: boolean; hiddenItems: ContextMenuHiddenItems}}
   */
  validateTabContextMenu() {
    const tabmixPrefs = Services.prefs.getBranch("extensions.tabmix.");
    const isFirstRun = !tabmixPrefs.prefHasUserValue("tabContextMenu");
    /** @type {ContextMenuHiddenItems} */
    const hiddenItems = {firefox: [], tabmix: []};

    if (!isFirstRun) {
      // If not first run, try to get existing hidden items and validate them
      try {
        const json = tabmixPrefs.getStringPref("tabContextMenu");
        /** @type {ContextMenuHiddenItems} */
        const tempItems = JSON.parse(json);

        // Validate the structure has expected keys
        if (
          tempItems &&
          typeof tempItems === "object" &&
          Array.isArray(tempItems.firefox) &&
          Array.isArray(tempItems.tabmix)
        ) {
          // Filter out invalid items
          hiddenItems.firefox = tempItems.firefox.filter(id => id in TAB_CONTEXT_MAPPING.firefox);
          hiddenItems.tabmix = tempItems.tabmix.filter(id => id in TAB_CONTEXT_MAPPING.tabmix);
          return {isFirstRun, hiddenItems};
        }
      } catch (ex) {
        console.error("Tabmix Error: Invalid tabContextMenu preference", ex);
      }

      console.error("Tabmix Error: Invalid tabContextMenu structure, treating as first run");
    }

    return {
      isFirstRun: true,
      hiddenItems: structuredClone(TAB_CONTEXT_MAPPING.defaultHidden),
    };
  },

  /**
   * Process preferences for a given namespace (firefox or tabmix)
   *
   * @param {"firefox" | "tabmix"} namespace - The namespace to process
   *   ('firefox' or 'tabmix')
   * @param {ContextMenuHiddenItems} hiddenItems - The current hidden items
   * @returns {boolean} - Whether migration was needed
   */
  _processNamespacePrefs(namespace, hiddenItems) {
    /** @type {string[]} */
    const keysToRemove = [];
    /** @type {string[]} */
    const keysToAdd = [];

    for (const [id, oldPref] of Object.entries(TAB_CONTEXT_MAPPING[namespace])) {
      try {
        if (TabmixPrefs.prefHasUserValue(oldPref)) {
          const isVisible = TabmixPrefs.getBoolPref(oldPref);
          if (isVisible) {
            keysToRemove.push(id);
          } else {
            keysToAdd.push(id);
          }

          // Clear old preference after migration
          TabmixPrefs.clearUserPref(oldPref);
        }
        // No need for isFirstRun check here as hiddenItems is already initialized
        // with default values in validateTabContextMenu
      } catch (ex) {
        console.error(`Tabmix Error, failed processing ${namespace} preference ${oldPref}:`, ex);
      }
    }

    if (keysToRemove.length || keysToAdd.length) {
      const hiddenList = hiddenItems[namespace].filter(key => !keysToRemove.includes(key));
      hiddenItems[namespace] = [...hiddenList, ...keysToAdd];

      return true;
    }

    return false;
  },

  /**
   * Convert all old boolean tab context menu preferences to a single JSON
   * string with shortened IDs
   */
  migrateTabContextPrefs() {
    const {isFirstRun, hiddenItems} = this.validateTabContextMenu();

    // Process both namespaces and track if migration was needed
    const firefoxMigrationNeeded = this._processNamespacePrefs("firefox", hiddenItems);
    const tabmixMigrationNeeded = this._processNamespacePrefs("tabmix", hiddenItems);

    // TODO: get default prefs

    // Save the preferences if anything changed or it's the first run
    if (firefoxMigrationNeeded || tabmixMigrationNeeded || isFirstRun) {
      TabmixPrefs.setStringPref("tabContextMenu", JSON.stringify(hiddenItems));
    }
  },

  /** Run all preference migrations as needed */
  runAllMigrations() {
    this.migrateTabContextPrefs();

    // Add other migrations here as needed
  },
};
