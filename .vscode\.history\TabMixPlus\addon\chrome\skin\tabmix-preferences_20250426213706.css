/********** preferences ********/

@namespace html url("http://www.w3.org/1999/xhtml");

/* Global Styles */
radio[pane] {
  padding: 5px 5px 1px 4px;
}

radio[pane="paneLinks"] {
  list-style-image: url("icons/links.png");
}

radio[pane="paneEvents"] {
  list-style-image: url("icons/events.png");
}

radio[pane="paneAppearance"] {
  list-style-image: url("icons/appearance.png");
}

radio[pane="paneMouse"] {
  list-style-image: url("icons/mouse.png");
}

radio[pane="paneMenu"] {
  list-style-image: url("icons/menu.png");
}

radio[pane="paneSession"] {
  list-style-image: url("icons/session-manager-24x24.png");
}

radio[pane="paneIncompatible"] {
  list-style-image: url("chrome://global/skin/icons/error.svg") !important;
  -moz-context-properties: fill;
  fill: var(--in-content-danger-button-background, #e22850);
  visibility: collapse;
}

radio[pane="paneIncompatible"] .radio-icon {
  width: 24px;
  height: 24px;
}

radio[pane="paneIncompatible"][show="true"] {
  visibility: visible;
}

menulist {
  min-height: 23.2px;
}

.content-help {
  appearance: none;
  margin: 0;
  min-width: 20px;
  border: 0 solid transparent;
  background-color: transparent;
  color: transparent;
}

.content-help,
.dialog-button[icon="help"] {
  list-style-image: url("chrome://tabmixplus/skin/question-16.png");
}

.content-help,
.content-help:hover:active {
  opacity: 1;
}

.content-help:not(:active):hover {
  opacity: 0.7;
}

.dialog-button[icon="help"] .button-icon {
  display: inline-flex !important; /* for Linux */
  margin-right: 6px;
}

.dialog-button[dlgtype="extra2"] .button-icon {
  margin-inline-start: 5px;
  order: 1;
  list-style-image: url("arrow-up.svg");
  -moz-context-properties: fill;
  fill: currentcolor;
}

.dialog-button[icon="help"][disabled="true"] .button-icon{
  opacity: 0.4;
}

.dialog-button-box > button,
.dialog-button {
  margin: 2px;
}

.donate-button-container {
  padding-bottom: 0;
}

.donate-button-container > .donate-button {
  appearance: none;
  cursor: pointer;
  background: url("https://img.shields.io/badge/Donate-PayPal-green.svg") no-repeat center/100%;
  min-height: 26px;
  width: 100px;
}

.donate-button-container > .donate-button:hover {
  box-shadow: inset 0 0 0 3px rgb(83 83 83);
  border-radius: 3px;
}

tabpanel {
  flex-direction: column;
}

.bottom-separator {
  height: 0 !important;
  margin-bottom: 0 !important;
}

.subtabs .tab-text {
  display: inline-block;
  min-width: 50px;
  text-align: center;
}

tab.subtabs:focus-visible > .tab-middle > .tab-text {
  outline: none;
}

/* Tabmix toolbar button icons */
#_btn_sessionmanager {
  list-style-image: url("icons/session-manager-24x24.png");
}

#_tabmix-closedTabsButton {
  list-style-image: url("icons/closed-tabs-24x24.png");
}

#_tabmix-closedWindowsButton {
  list-style-image: url("icons/closed-win-24x24.png");
}

#_tabmix-alltabs-button {
  list-style-image: url("icons/tab-list-24x24.png");
}

.tabmixbuttons {
  align-items: center;
  padding: 3px;
}

#TabMIxPreferences {
  display: flex;
  flex-direction: column;
  min-height: 550px;
}

/* Chromifox theme force button height to 25px */
#TabMIxPreferences[chromifox="true"] .spinbuttons-button {
  max-height: 11px !important;
}

/***** for default theme *****/

/* hide top tabbox tabs for link and session panel */
.tabs-hidden > tab {
  visibility: hidden;
}

/* fix small bug in firefox tabbox.css
   tabs moves left or right when selection changed
   we change the border from 2px to none so
   we need also to change the padding
*/
#TabMIxPreferences:-moz-locale-dir(ltr) tab[beforeselected="true"],
#TabMIxPreferences:-moz-locale-dir(rtl) tab[selected="true"] + tab {
  padding-right: 2px ;
}

#TabMIxPreferences:-moz-locale-dir(ltr) tab[selected="true"] + tab,
#TabMIxPreferences:-moz-locale-dir(rtl) tab[beforeselected="true"] {
  padding-left: 2px ;
}

.subtabs[selected="true"][first-tab="true"] {
  padding-right: 6px ;
  padding-left: 4px ;
}

.subtabs[selected="true"][last-tab="true"] {
  padding-right: 4px;
}

#TabMIxPreferences:not([ubuntu]) .groupbox-panels:not(.tabclick) {
  padding: 3px 3px 6px;
}

.groupbox-tabbox {
  margin: 3px;
}

/* for locales with wide tabs, align right end with tabpanels */
tabbox > tabs {
  -moz-margin-end: 2px;
}

#TabMIxPreferences:not([linux], [mac]) .groupbox-tabbox > tabs {
  margin-bottom: -2px;
}

.extraIcons .checkbox-icon {
	padding: 2px 0;
	margin-top: 4px;
  margin-inline-end: 3px;
	max-height: 11px !important;
	width: 10px;
}

#extraIcons-locked .checkbox-icon {
  background-image: url("locked.png");
}

#extraIcons-protected .checkbox-icon {
  background-image: url("protected.png");
  background-position: 10px 11px;
}

#extraIcons-autoreload .checkbox-icon {
  background-image: url("autoreload.png");
  background-position: 10px 0;
}

.clicktab-popup {
  flex-direction: column;
}

.clicktab-popup > * > .arrowscrollbox-scrollbox {
  overflow: auto !important;
}

.clicktab-popup > * > .autorepeatbutton-up,
.clicktab-popup > * > .autorepeatbutton-down {
  display: none !important;
}

#tabBarDisplay[tstInstalled] label[TSTdisabled="true"] {
  opacity: 0.5;
}

/* rules to make the new option window look the same as the old one */
radio[pane] > image {
  width: 24px;
  height: 24px;
}

prefpane {
  height: fit-content;
  padding: 3px 1px 1px;
}

tabbox {
  flex: 1;
  padding: 0 1px;
}

prefpane > .content-box > tabbox {
  min-width: 525px;
  min-height: var(--content-box-max-pane-height, 464px);
}

prefwindow .paneSelector {
  min-width: 530px;
}

tabbox > tabpanels {
  flex: 1;
}

tabbox > tabpanels:last-child {
  padding-bottom: 6px;
}

/* for Mac */
#TabMIxPreferences[mac] prefpane > .content-box > tabbox {
  height: var(--content-box-max-pane-height, 468px);
}

#TabMIxPreferences[mac] .groupbox-tabbox {
  margin: 3px 5px;
}

#TabMIxPreferences[mac] groupbox {
  margin-top: 3px;
  margin-bottom: 1px;
  padding-top: 2px;
}

#TabMIxPreferences[mac] prefpane .groupbox-title {
  background-image: none;
  padding-top: 2px;
  margin-bottom: 2px;
}

#sessionsPanel > groupbox > vbox {
  height: 55px;
}

#TabMIxPreferences[mac] prefpane .groupbox-body {
  padding-top: 2px;
  margin-bottom: 2px;
}

#sessionsPanel[manager="firefox"] tab.firefox {
  visibility: hidden;
}

.menu checkbox {
  padding-top: 0;
}

separator.groove {
  display: block;
  margin-inline: -8px;
}

html|fieldset {
  margin-block: 3px;
}

html|fieldset.flex {
  display: flex;
}

html|fieldset.flex.column {
  flex-direction: column;
}

html|fieldset.flex.column > spacer[flex="1"] {
  flex: 1;
}

html|fieldset.grid,
.grid {
  display: grid;
}

html|fieldset[flex="1"] {
  flex: 1 auto;
}

#ClickTabbar .multiselectitem,
#mouseclick_tabbox[selectedIndex="0"] #ClickTab .multiselectitem {
  display: none;
}

.bold-label {
  -moz-margin-start: 2px;
  -moz-padding-start: 1px;
  font-weight: 600;
}
