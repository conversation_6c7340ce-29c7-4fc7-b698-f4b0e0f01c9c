<?xml version="1.0"?>

<?xml-stylesheet href="chrome://global/skin/" type="text/css"?>
<!-- load autoReload_properties.css before preferences.css -->
<?xml-stylesheet href="chrome://tabmixplus/content/overlay/autoReload_properties.css"?>
<?xml-stylesheet href="chrome://tabmixplus/skin/preferences.css"?>
<?xml-stylesheet href="chrome://tabmixplus/content/overlay/autoReload.css"?>

<!DOCTYPE dialog [
<!ENTITY % tabmixDTD SYSTEM "chrome://tabmixplus/locale/tabmix.dtd" >
%tabmixDTD;
<!ENTITY % miscDTD SYSTEM "chrome://tabmixplus/locale/misc.dtd" >
%miscDTD;
]>

<dialog id="reloadevery_custom_dialog"
        title="&specifyreload.label;"
        buttons="accept,cancel"
        onload="load();"
        xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"
        xmlns:html="http://www.w3.org/1999/xhtml"
        tabindex="0">

  <script type="application/javascript" src="chrome://tabmixplus/content/preferences/numberinput.js"/>
  <script type="application/javascript" src="chrome://tabmixplus/content/overlay/autoReload.js"/>

  <html:fieldset>
    <html:legend>&reloadtime.label1;</html:legend>
    <div class="container">
      <div class="combined-element">
        <html:input id="autoreload_minutes" type="number" required="required" min="0" size="4" maxlength="4"
          oncommand="updateOkButtonDisabledState();"
          oninput="onInput(this);"/>
        <div class="select-button" onmousedown="openPopup(this);"></div>
        <menupopup class="select-menupopup"
          oncommand="onSelect(event)"
          onpopupshowing="this.style.width=this.parentNode.getBoundingClientRect().width+'px';">
          <menuitem value="0" label="0"/>
          <menuitem value="1" label="1"/>
          <menuitem value="5" label="5"/>
          <menuitem value="10" label="10"/>
          <menuitem value="15" label="15"/>
          <menuitem value="20" label="20"/>
          <menuitem value="30" label="30"/>
        </menupopup>
      </div>
      <label control="autoreload_minutes" value="&minutes.label;"/>
    </div>

    <div class="container">
      <div class="combined-element">
        <html:input list="sec" id="autoreload_seconds" type="number" required="required" min="0" max="59" size="4" maxlength="4"
          oncommand="updateOkButtonDisabledState();"
          oninput="onInput(this);"/>
        <div class="select-button" onmousedown="openPopup(this);"></div>
        <menupopup class="select-menupopup"
          oncommand="onSelect(event)"
          onpopupshowing="this.style.width=this.parentNode.getBoundingClientRect().width+'px';">
          <menuitem value="0" label="0"/>
          <menuitem value="10" label="10"/>
          <menuitem value="20" label="20"/>
          <menuitem value="30" label="30"/>
          <menuitem value="45" label="45"/>
        </menupopup>
      </div>
      <label control="autoreload_seconds" value="&seconds.label;"/>
    </div>
  </html:fieldset>

</dialog>
