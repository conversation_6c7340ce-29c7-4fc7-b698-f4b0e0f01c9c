<?xml version="1.0"?>

<?xml-stylesheet href="chrome://global/skin/" type="text/css"?>
<?xml-stylesheet href="chrome://tabmixplus/skin/preferences.css"?>

<!DOCTYPE window [
<!ENTITY % dialogDTD SYSTEM "chrome://tabmixplus/locale/pref-tabmix.dtd" >
%dialogDTD;
<!ENTITY % filetypeDTD SYSTEM "chrome://tabmixplus/locale/pref-filetype.dtd" >
%filetypeDTD;
]>

<window id="pref-TMP-filetype"
        class="system-font-size"
        windowtype="mozilla:tabmixopt-filetype"
        xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"
        xmlns:html="http://www.w3.org/1999/xhtml"
        title="&filetype.options;"
        onload="Init()"
        persist="screenX screenY">
<dialog buttons="accept,cancel,extra1">
   <script type="application/javascript" src="chrome://tabmixplus/content/utils.js"/>
   <script type="application/javascript" src="chrome://tabmixplus/content/preferences/subdialogs/pref-filetype.js"/>

   <html:fieldset class="filetype-content">
        <richlistbox id="filetypeList" rows="10" style="height: 18em;" seltype="single" prefstring="extensions.tabmix.filetype" onselect="Select();"/>
        <hbox flex="1" align="center">
         <label value="&filetype.new;"/>
         <html:input id="filetypeEntry" type="text" oninput="Input();"/>
        </hbox>
        <hbox flex="1" pack="center">
         <button id="filetypeAdd" label="&filetype.add;" disabled="true" oncommand="Add();"/>
         <button id="filetypeEdit" label="&filetype.edit;" disabled="true" oncommand="Mod();"/>
         <button id="filetypeDelete" label="&filetype.delete;" oncommand="Del();"/>
        </hbox>
        <hbox>
         <button id="filetypeRestore" label="&settings.default;" oncommand="Restore();"/>
        </hbox>
   </html:fieldset>
   <separator class="filetype-separator"/>
   <hbox class="dialog-button-box filetype-buttons" pack="end">
     <button dlgtype="accept" class="dialog-button" icon="accept"/>
     <button dlgtype="cancel" class="dialog-button" icon="cancel"/>
     <button dlgtype="extra1" class="dialog-button" hidden="true" icon="help"/>
   </hbox>
</dialog>
</window>
