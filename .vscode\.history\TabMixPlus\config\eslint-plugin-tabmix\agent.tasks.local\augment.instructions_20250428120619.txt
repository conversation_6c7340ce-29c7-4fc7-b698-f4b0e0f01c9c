TASK INSTRUCTIONS

the logic for migrateTabContextPrefs is not good

first, do you think that it is better to use one preference for tabContextMenu
by using {"Firefox": [], "TabMix": []} or two preferences one for Firefox and one for TabMix

migrateTabContextPrefs need to handle 2 scenarios:
 - when user start the browser
 - when user import preferences from old version


when user start the browser:
1.if user has preference for `extensions.tabmix.tabContextMenu`
we know it is not the first time the browser starts since this migration
add to extensions.tabmix.tabContextMenu only preference from `TAB_CONTEXT_MAPPING`
that with prefHasUserValue and getBoolPref value false, remove the ones that are with
prefHasUserValue and getBoolPref value true
clear all old preferences from `TAB_CONTEXT_MAPPING` that has prefHasUserValue

2.if user has no preference for `extensions.tabmix.tabContextMenu`
this is the first time the browser starts since this migration
do the same as above, but also add to extensions.tabmix.tabContextMenu all the preference
with default value false from addon/defaults/preferences/tabmix.js
update `TAB_CONTEXT_MAPPING` with 3rd column with the default value from tabmix.js
add the 3rd param only to those with false

when user import preferences from old version:
 same as `when user start the browser` 1

let me know if this logic is good and clear
