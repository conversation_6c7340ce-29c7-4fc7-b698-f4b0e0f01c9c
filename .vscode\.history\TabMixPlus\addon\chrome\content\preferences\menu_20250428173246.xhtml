<?xml version="1.0"?>

<!DOCTYPE overlay [
<!ENTITY % pref-tabmixDTD SYSTEM "chrome://tabmixplus/locale/pref-tabmix.dtd">
%pref-tabmixDTD;
<!ENTITY % tabmixDTD SYSTEM "chrome://tabmixplus/locale/tabmix.dtd">
%tabmixDTD;
<!ENTITY % shortcutsDTD SYSTEM "chrome://tabmixplus/locale/shortcuts.dtd">
%shortcutsDTD;
]>

<overlay id="MenuPaneOverlay"
         xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"
         xmlns:html="http://www.w3.org/1999/xhtml">

  <prefpane id="paneMenu" onpaneload="gMenuPane.init();">
    <!-- scripts -->
    <script type="application/javascript" src="chrome://tabmixplus/content/preferences/menu.js"/>
    <script type="application/javascript" src="chrome://tabmixplus/content/preferences/shortcuts.js"/>

    <!-- preferences - list all preferences in this pane -->
    <preferences>
      <preference id="pref_menu"
                  name="extensions.tabmix.menu.selectedTabIndex"                            type="int"/>
      <preference id="pref_showOnTabbar"
                  name="extensions.tabmix.showTabContextMenuOnTabbar"                       type="bool"/>
      <preference id="pref_tabContextMenu"     name="extensions.tabmix.tabContextMenu"      type="string"/>
      <preference id="pref_tabContextMenu_menuOrder"
                  name="extensions.tabmix.tabContextMenu.menuOrder"                         type="int"/>
      <preference id="pref_showNewTab"         name="extensions.tabmix.newTabMenu"          type="bool"/>
      <preference id="pref_showDuplicate"      name="extensions.tabmix.duplicateMenu"       type="bool"/>
      <preference id="pref_showDuplicateinWin" name="extensions.tabmix.duplicateinWinMenu"  type="bool"/>
      <preference id="pref_showDetachTab"      name="extensions.tabmix.detachTabMenu"       type="bool"/>
      <preference id="pref_selectAllTabs"      name="extensions.tabmix.selectAllTabs"       type="bool"/>
      <preference id="pref_showMergeWin"       name="extensions.tabmix.showMergeWindow"     type="bool"/>
      <preference id="pref_muteTab"            name="extensions.tabmix.muteTabMenu"         type="bool"/>
      <preference id="pref_pinTab"             name="extensions.tabmix.pinTabMenu"          type="bool"/>
      <preference id="pref_showRenametab"      name="extensions.tabmix.renameTabMenu"       type="bool"/>
      <preference id="pref_showCopyTabUrl"     name="extensions.tabmix.copyTabUrlMenu"      type="bool"/>

      <preference id="pref_reopenInContainer"  name="extensions.tabmix.reopenInContainer"   type="bool"/>
      <preference id="pref_moveTabOptions"     name="extensions.tabmix.moveTabOptions"      type="bool"/>
      <preference id="pref_sendTabToDevice"    name="extensions.tabmix.sendTabToDevice"     type="bool"/>
      <preference id="pref_shareTabURL"        name="extensions.tabmix.shareTabURL"         type="bool"/>

      <preference id="pref_showReloadTab"      name="extensions.tabmix.reloadTabMenu"       type="bool"/>
      <preference id="pref_showAutoReload"     name="extensions.tabmix.autoReloadMenu"      type="bool"/>
      <preference id="pref_reloadTabOptions"   name="extensions.tabmix.reloadTabOptions"    type="bool"/>
      <preference id="pref_showBmkTab"         name="extensions.tabmix.bookmarkTabMenu"     type="bool"/>
      <preference id="pref_showUndoClose"      name="extensions.tabmix.undoCloseTabMenu"    type="bool"/>
      <preference id="pref_showUndoCloseList"  name="extensions.tabmix.undoCloseListMenu"   type="bool"/>
      <preference id="pref_showCloseTab"       name="extensions.tabmix.closeTabMenu"        type="bool"/>
      <preference id="pref_closeDuplicateTabs" name="extensions.tabmix.closeDuplicateTabs"  type="bool"/>
      <preference id="pref_closeTabOptions"    name="extensions.tabmix.closeTabOptions"     type="bool"/>
      <preference id="pref_showDocShell"       name="extensions.tabmix.docShellMenu"        type="bool"/>
      <preference id="pref_freezeTabMenu"      name="extensions.tabmix.freezeTabMenu"       type="bool"/>
      <preference id="pref_protectTabMenu"     name="extensions.tabmix.protectTabMenu"      type="bool"/>
      <preference id="pref_lockTabMenu"        name="extensions.tabmix.lockTabMenu"         type="bool"/>
      <preference id="pref_showBmkTabs"        name="extensions.tabmix.bookmarkTabsMenu"    type="bool"/>
      <preference id="pref_showLinkHere"       name="extensions.tabmix.openLinkHere"        type="bool"/>
      <preference id="pref_selectTab1"         name="browser.tabs.loadInBackground"
                                                                           inverted="true"  type="bool"
                  onchange="if (typeof gEventsPane == 'undefined') gMenuPane.setInverseLinkLabel();"/>
      <preference id="pref_showInverseLink"    name="extensions.tabmix.openInverseLink"     type="bool"/>
      <preference id="pref_openAllLinks"       name="extensions.tabmix.openAllLinks"        type="bool"/>
      <preference id="pref_linkWithHist"       name="extensions.tabmix.linkWithHistory"     type="bool"/>
      <preference id="pref_autoreloadTab"      name="extensions.tabmix.autoReloadContent"   type="bool"/>
      <preference id="pref_closetab"           name="extensions.tabmix.closeTabContent"     type="bool"/>
      <preference id="pref_duplicateTabContent"
                  name="extensions.tabmix.duplicateTabContent"                              type="bool"/>
      <preference id="pref_duplicateWinContent"
                  name="extensions.tabmix.duplicateWinContent"                              type="bool"/>
      <preference id="pref_detachTabContent"   name="extensions.tabmix.detachTabContent"    type="bool"/>
      <preference id="pref_mergeContent"       name="extensions.tabmix.mergeWindowContent"  type="bool"/>
      <preference id="pref_freezeTabContent"   name="extensions.tabmix.freezeTabContent"    type="bool"/>
      <preference id="pref_protectTabContent"  name="extensions.tabmix.protectTabContent"   type="bool"/>
      <preference id="pref_lockTabContent"     name="extensions.tabmix.lockTabContent"      type="bool"/>
      <preference id="pref_tabsList"           name="extensions.tabmix.tabsList"            type="bool"/>
      <preference id="pref_showUndoCloseListContent"
                  name="extensions.tabmix.undoCloseListContent"                             type="bool"/>
      <preference id="pref_showUndoCloseContent"
                  name="extensions.tabmix.undoCloseTabContent"                              type="bool"/>
      <preference id="pref_optionsToolsMenu"   name="extensions.tabmix.optionsToolMenu"     type="bool"/>
      <preference id="pref_closedWinToolsMenu" name="extensions.tabmix.closedWinToolsMenu"  type="bool"/>
      <preference id="pref_shortcuts"          name="extensions.tabmix.shortcuts"           type="string"
                  onchange="gMenuPane.initializeShortcuts();"/>
      <preference id="pref_slideshow"          name="extensions.tabmix.slideDelay"          type="int"/>
   </preferences>


    <!-- pane content -->
    <tabbox
            onselect="gPrefWindow.tabSelectionChanged(event);">
      <tabs id="menu">
        <tab label="&context.tab;" class="subtabs" helpTopic="Menu_-_Tab_Context_Menu"/>
        <tab label="Dynamic Tab Menu" class="subtabs" helpTopic="Menu_-_Tab_Context_Menu_Dynamic"/>
        <tab label="&context.main;" class="subtabs" helpTopic="Menu_-_Main_Context_Menu"/>
        <tab label="&context.tools;" class="subtabs" helpTopic="Menu_-_Tools_Menu"/>
        <tab label="&shortcuts.label;" class="subtabs" helpTopic="Menu_-_Shortcuts"/>
      </tabs>
      <tabpanels class="menu">
        <tabpanel>
          <html:fieldset flex="1">
          <html:legend>&showtabBarContext.label;</html:legend>
            <checkbox_tmp id="showOnTabbar" label="&showOnTabbar.label;" preference="pref_showOnTabbar"/>
            <hbox align="center">
              <label value="Order of items in the context menu:" control="tabContextMenu_menuOrder"/>
              <menulist id="tabContextMenu_menuOrder" orient="horizontal" preference="pref_tabContextMenu_menuOrder">
                <menupopup>
                  <menuitem value="0" label="Tab Mix Plus - order"/>
                  <menuitem value="1" label="Firefox - build-in order"/>
                </menupopup>
              </menulist>
            </hbox>
            <separator class="groove"/>
            <div style="overflow: auto; display: grid; grid-template-columns: 1fr 1fr; max-height: 442px;">
              <vbox align="start">
                <!-- New tab -->
                <checkbox_tmp id="showNewTab" data-lazy-l10n-id="menu-file-new-tab" preference="pref_showNewTab"/>
                <!-- mute/unmute tabs -->
                <checkbox_tmp id="muteTab" preference="pref_muteTab"/>
                <checkbox_tmp id="unmuteTab" hidden="true"/>
                <!-- pin/unpin tabs -->
                <checkbox_tmp id="pinTab" preference="pref_pinTab"/>
                <!-- Duplicate tab -->
                <checkbox_tmp id="showDuplicate" label="&duplicateTabMenu.label;" preference="pref_showDuplicate"/>
                <!-- Duplicate in Window -->
                <checkbox_tmp id="showDuplicateinWin" label="&duplicateinWin.label;" preference="pref_showDuplicateinWin" observes="obs_singleWindow"/>
                <!-- Move Tabs -->
                <checkbox_tmp id="moveTabOptions" data-lazy-l10n-id="tab-context-move-tabs" data-l10n-args='{"tabCount": 2}' preference="pref_moveTabOptions"/>
                <checkbox_tmp id="sendTabToDevice" preference="pref_sendTabToDevice"
                              data-lazy-l10n-id="tab-context-send-tabs-to-device"
                              data-l10n-args='{"tabCount": 1}'/>
                <checkbox_tmp id="shareTabURL" data-lazy-l10n-id="tab-context-share-url" preference="pref_shareTabURL"/>
                <!-- Merge Windows -->
                <checkbox_tmp id="showMergeWin" label="&mergeContext.label;" preference="pref_showMergeWin"/>
                <!-- Rename tab -->
                <checkbox_tmp id="showRenametab" label="&renametab.label;" preference="pref_showRenametab"/>
                <!-- Copy tab Url -->
                <checkbox_tmp id="showCopyTabUrl" label="&copytaburl.label;" preference="pref_showCopyTabUrl"/>
                <checkbox_tmp id="reopenInContainer" data-lazy-l10n-id="tab-context-open-in-new-container-tab" preference="pref_reopenInContainer"/>
                <!-- Select All Tabs -->
                <checkbox_tmp id="showSelectAllTabs" data-lazy-l10n-id="select-all-tabs" preference="pref_selectAllTabs"/>
              </vbox>
              <vbox align="start">
                <!-- Reload Tab Every -->
                <checkbox_tmp id="showAutoReload" label="&autoReloadTab.label;" preference="pref_showAutoReload"/>
                <!-- Reload Multiple Tabs -->
                <checkbox_tmp id="reloadTabOptions" label="Reload Multiple Tabs" preference="pref_reloadTabOptions"/>
                <!-- Reload tab -->
                <checkbox_tmp id="showReloadTab" data-lazy-l10n-id="reload-tab"  preference="pref_showReloadTab"/>
                <!-- Undo close tabs -->
                <checkbox_tmp id="showUndoClose" data-lazy-l10n-id="tab-context-reopen-closed-tabs" data-l10n-args='{"tabCount": 1}' preference="pref_showUndoClose" observes="obs_undoClose"/>
                <!-- Closed tabs list -->
                <checkbox_tmp id="showUndoCloseList" label="&undoCloseListMenu.label;" preference="pref_showUndoCloseList" observes="obs_undoClose"/>
                <!-- Close tabs -->
                <checkbox_tmp id="showCloseTab" data-lazy-l10n-id="tab-context-close-n-tabs" data-l10n-args='{"tabCount": 1}' preference="pref_showCloseTab"/>
                <!-- Close DuplicateTabs tabs -->
                <checkbox_tmp id="closeDuplicateTabs" data-lazy-l10n-id="tab-context-close-duplicate-tabs" preference="pref_closeDuplicateTabs"/>
                <!-- Close Multiple Tabs -->
                <checkbox_tmp id="closeTabOptions" data-lazy-l10n-id="tab-context-close-multiple-tabs" preference="pref_closeTabOptions"/>
                <!-- docShell -->
                <checkbox_tmp id="showDocShell" label="&docShellMenu.label;" preference="pref_showDocShell"/>
                <!-- freeze tab -->
                <checkbox_tmp id="freezeTabMenu" label="&freezeTabMenu.label;" tooltiptext="&clicktab.freezetab;" preference="pref_freezeTabMenu"/>
                <!-- protect tab -->
                <checkbox_tmp id="protectTabMenu" label="&protectTabMenu.label;" tooltiptext="&protectTabMenu.tooltip;" preference="pref_protectTabMenu"/>
                <!-- lock tab -->
                <checkbox_tmp id="lockTabMenu" label="&lockTabMenu.label;" tooltiptext="&lockTabMenu.tooltip;" preference="pref_lockTabMenu"/>
                <!-- Bookmark tab -->
                <checkbox_tmp id="showBmkTab" data-lazy-l10n-id="tab-context-bookmark-tab" preference="pref_showBmkTab"/>
                <!-- bookmark tabs -->
                <checkbox_tmp id="showBmkTabs" data-lazy-l10n-id="menu-bookmarks-all-tabs" preference="pref_showBmkTabs"/>
              </vbox>
            </div>
          </html:fieldset>
        </tabpanel>
        <tabpanel>
          <html:fieldset flex="1">
            <html:legend>&showtabBarContext.label;</html:legend>
            <checkbox_tmp id="showOnTabbar" label="&showOnTabbar.label;" preference="pref_showOnTabbar"/>
            <hbox align="center">
              <label value="Order of items in the context menu:" control="tabContextMenu_menuOrder"/>
              <menulist id="tabContextMenu_menuOrder" orient="horizontal" preference="pref_tabContextMenu_menuOrder">
                <menupopup>
                  <menuitem value="0" label="Tab Mix Plus order"/>
                  <menuitem value="1" label="Firefox build-in order"/>
                </menupopup>
              </menulist>
            </hbox>
            <separator class="groove"/>
            <div id="tab-context-menu-container" style="overflow: auto; display: grid; grid-template-columns: 1fr 1fr 1fr; margin-bottom:1rem"
                 onsyncfrompreference="return gMenuPane.tabContextMenuFromPreference(this);"
                 onsynctopreference="return gMenuPane.tabContextMenuToPreference(this);"
                 preference-editable="true"
                 preference="pref_tabContextMenu">
              <vbox align="start" id="column-1"/>
              <vbox align="start" id="column-2"/>
              <vbox align="start" id="column-3"/>
            </div>
            <label value="bold items are from Tab Mix Plus"/>
          </html:fieldset>
        </tabpanel>
        <tabpanel>
          <html:fieldset flex="1">
            <html:legend>&showContentAreaContext.label;</html:legend>
            <div style="overflow: auto; display: grid; grid-template-columns: 1fr 1fr;">
              <vbox align="start">
                <!-- links in current tab -->
                <checkbox_tmp id="showLinkHere" label="&linkhere.label;" preference="pref_showLinkHere"/>
                <!-- links in inverse tab -->
                <checkbox_tmp id="showInverseLink" fglabel="&linkForegroundTab.label;" bglabel="&linkBackgroundTab.label;" preference="pref_showInverseLink"/>
                <!-- multiple links -->
                <checkbox_tmp id="openAllLinks" label="&openalllinks.label;" preference="pref_openAllLinks"/>
                <!-- link in duplicate tab -->
                <checkbox_tmp id="linkWithHist" label="&linkwithhistory.label;" preference="pref_linkWithHist"/>
                <!-- reload every -->
                <checkbox_tmp id="autoreloadTab" label="&autoReloadTab.label;" preference="pref_autoreloadTab"/>
                <!-- Close tab -->
                <checkbox_tmp id="closetab" data-lazy-l10n-id="tab-context-close-n-tabs" data-l10n-args='{"tabCount": 1}' preference="pref_closetab"/>
                <!-- Duplicate tab -->
                <checkbox_tmp id="duplicateTabContent" label="&duplicateTabMenu.label;" preference="pref_duplicateTabContent"/>
                <!-- Duplicate tab in Win-->
                <checkbox_tmp id="duplicateWinContent" label="&duplicateinWin.label;" preference="pref_duplicateWinContent" observes="obs_singleWindow"/>
              </vbox>
              <vbox align="start">
                <!-- Detach Tab-->
                <checkbox_tmp id="detachTabContent" label="&detachTab.label;" preference="pref_detachTabContent" observes="obs_singleWindow"/>
                <!-- Merge Content-->
                <checkbox_tmp id="mergeContent" label="&mergeContext.label;" preference="pref_mergeContent"/>
                <!-- freeze tab -->
                <checkbox_tmp id="freezeTabContent" label="&freezeTabMenu.label;" tooltiptext="&clicktab.freezetab;" preference="pref_freezeTabContent"/>
                <!-- protect tab -->
                <checkbox_tmp id="protectTabContent" label="&protectTabMenu.label;" tooltiptext="&protectTabMenu.tooltip;" preference="pref_protectTabContent"/>
                <!-- lock tab -->
                <checkbox_tmp id="lockTabContent" label="&lockTabMenu.label;" tooltiptext="&lockTabMenu.tooltip;" preference="pref_lockTabContent"/>
                <!-- Opened tabs list -->
                <checkbox_tmp id="tabsList" label="&tabsList.label;" preference="pref_tabsList"/>
                <!-- Closed tabs list -->
                <checkbox_tmp id="showUndoCloseListContent" label="&undoCloseListMenu.label;" preference="pref_showUndoCloseListContent" observes="obs_undoClose"/>
                <!-- Undo close tabs -->
                <checkbox_tmp id="showUndoCloseContent" data-lazy-l10n-id="tab-context-reopen-closed-tabs" data-l10n-args='{"tabCount": 1}' preference="pref_showUndoCloseContent" observes="obs_undoClose"/>
              </vbox>
            </div>
          </html:fieldset>
        </tabpanel>
        <tabpanel>
          <html:fieldset flex="1">
            <html:legend>&showToolsMenu.label;</html:legend>
            <!-- tab mix options in Tools Menu -->
            <div style="overflow: auto; display: grid; grid-template-columns: 1fr 1fr;">
              <vbox align="start">
                <checkbox_tmp id="optionsToolsMenu" preference="pref_optionsToolsMenu"
                          label="&page.header.title;…"/>
                <checkbox_tmp id="closedWinToolsMenu" preference="pref_closedWinToolsMenu"
                          label="&closedWin.label;"/>
              </vbox>
            </div>
          </html:fieldset>
        </tabpanel>
        <tabpanel id="shortcuts-panel" hide-shortcut-warning="true"
              hide-unused-shortcuts="false" usedKeys="false"
              persist="hide-shortcut-warning hide-unused-shortcuts"
              onmousedown="if (event.originalTarget.className == 'shortcut-image') return;
                  var shortcut = this.shortcut || null;
                  if (shortcut) {shortcut.updateFocus(false); this.shortcut = null;}"
        >
          <hbox>
            <label value="&shortcuts.edit;" flex="1"/>
            <spacer flex="1"/>
            <label value="&shortcuts.hideUnused;" show="&shortcuts.showAll;" hide="&shortcuts.hideUnused;"
                   class="text-link" id="hide-unused-shortcuts" persist="value"
                   onmousedown="gMenuPane.toggleLinkLabel(this);"/>
          </hbox>
          <hbox class="global-warning shortcut-warning-container" align="center">
            <vbox>
              <image class="warning-icon"/>
            </vbox>
            <description style="width: 24em;" class="global-warning-text" flex="1">
              &shortcuts.warning;.
            </description>
            <label value="&shortcuts.showDetails;" show="&shortcuts.showDetails;" hide="&shortcuts.hideDetails;"
                   class="text-link" id="hide-shortcut-warning" persist="value"
                   onmousedown="gMenuPane.toggleLinkLabel(this);"/>
          </hbox>
          <html:fieldset id="shortcut-group" class="grid" style="overflow: auto; height:18em;" flex="1">
            <shortcut id="newTab"          label="&clicktab.addtab;"/>
            <shortcut id="dupTab"          label="&clicktab.duplicatetab;"/>
            <shortcut id="dupTabToWin"     label="&clicktab.duplicatetabw;"/>
            <shortcut id="detachTab"       label="&clicktab.detachtab;"/>
            <shortcut id="togglePinTab"/>
            <shortcut id="protecttab"      label="&clicktab.protecttab;"/>
            <shortcut id="locktab"         label="&clicktab.locktab;"/>
            <shortcut id="freezetab"       label="&clicktab.freezetab;"/>
            <shortcut id="renametab"       label="&clicktab.renametab;"/>
            <shortcut id="copyTabUrl"      label="&clicktab.copyTabUrl;"/>
            <shortcut id="pasteTabUrl"     label="&clicktab.copyUrlFromClipboard;"/>
            <shortcut id="selectMerge"     label="&clicktab.selectMerge;"/>
            <shortcut id="mergeWin"        label="&clicktab.mergeTabs;"/>
            <shortcut id="addBookmark"     label="&clicktab.bookTab;"/>
            <shortcut id="bookmarkAllTabs" label="&clicktab.bookTabs;"/>
            <shortcut id="reload"          label="&clicktab.reloadtab;"/>
            <shortcut id="browserReload"   label="&clicktab.reloadtab;*"/>
            <shortcut id="reloadtabs"      label="&clicktab.reloadtabs;"/>
            <shortcut id="reloadothertabs" label="&clicktab.reloadothertabs;"/>
            <shortcut id="reloadlefttabs"  label="&clicktab.reloadlefttabs;"/>
            <shortcut id="reloadrighttabs" label="&clicktab.reloadrighttabs;"/>
            <shortcut id="autoReloadTab"   label="&clicktab.autoReloadTab;"/>
            <shortcut id="close"           data-lazy-l10n-id="tab-context-close-n-tabs" data-l10n-args='{"tabCount": 1}'/>
            <shortcut id="removeall"       label="&clicktab.removeall;"/>
            <shortcut id="removesimilar"   label="&clicktab.removesimilar;"/>
            <shortcut id="removeother"     label="&clicktab.removeother;"/>
            <shortcut id="removeleft"      label="&clicktab.removetoLeft;"/>
            <shortcut id="removeright"     label="&clicktab.removetoRight;"/>
            <shortcut id="undoCloseTab"    label="&clicktab.uctab;"/>
            <shortcut id="clearClosedTabs"/>
            <shortcut id="ucatab"          label="&clicktab.ucatab;"/>
            <shortcut id="switchToLast"    label="&shortcuts.switchToLast;"/>
            <shortcut id="toggleFLST"      label="&shortcuts.toggleFLST;"
                      tooltiptext="&shortcuts.toggleFLST.tooltip;"/>
            <shortcut id="slideShow"       label="&shortcuts.slideshow;"
                                          _label="&activateSlideshow.label;"/>
            <hbox align="center" class="indent">
              <label id="slideDelayLabel" observes="obs_slideDelay"/>
              <html:input id="slideshow.time" maxlength="3" size="3" preference="pref_slideshow"
                       observes="obs_slideDelay" type="number" required="required" min="0"/>
              <label value="&seconds.label;" observes="obs_slideDelay" class="timelabel"/>
            </hbox>
          </html:fieldset>
        </tabpanel>
      </tabpanels>
    </tabbox>

    <broadcasterset >
      <broadcaster id="obs_slideDelay"/>
    </broadcasterset >

  </prefpane>

</overlay>
