/* eslint-disable @stylistic/lines-around-comment */

/**
 * <PERSON><PERSON><PERSON> to handle migration of old preferences to new formats Runs during
 * browser startup or when importing saved preference files
 */

/**
 * Mapping of old tab context menu preferences to DOM element IDs
 *
 * @type [string, string][]
 */
const TAB_CONTEXT_MAPPING = [
  ["bookmarkTabsMenu", "context_bookmarkAllTabs"],
  ["bookmarkTabMenu", "context_bookmarkTab"],
  ["closeTabMenu", "context_closeTab"],
  ["closeTabOptions", "context_closeTabOptions"],
  ["closeDuplicateTabs", "context_closeDuplicateTabs"],
  ["detachTabMenu", "context_openTabInWindow"],
  ["duplicateMenu", "context_duplicateTab"],
  ["moveTabOptions", "context_moveTabOptions"],
  ["muteTabMenu", "context_toggleMuteTab"],
  ["newTabMenu", "context_openANewTab"],
  ["pinTabMenu", "context_pinTab"],
  ["reloadTabMenu", "context_reloadTab"],
  ["reloadTabOptions", "context_reloadTabOptions"],
  ["reopenInContainer", "context_reopenInContainer"],
  ["selectAllTabs", "context_selectAllTabs"],
  ["sendTabToDevice", "context_sendTabToDevice"],
  ["shareTabURL", "context_shareTabURL"],
  ["undoCloseTabMenu", "context_undoCloseTab"],
  ["autoReloadMenu", "tm-autoreloadTab_menu"],
  ["copyTabUrlMenu", "tm-copyTabUrl"],
  ["docShellMenu", "tm-docShell"],
  ["duplicateinWinMenu", "tm-duplicateinWin"],
  ["freezeTabMenu", "tm-freezeTab"],
  ["lockTabMenu", "tm-lockTab"],
  ["protectTabMenu", "tm-protectTab"],
  ["renameTabMenu", "tm-renameTab"],
  ["showMergeWindow", "tm-mergeWindowsTab"],
  ["undoCloseListMenu", "tm-undoCloseList"],
];

export const PreferenceMigrator = {
  /**
   * Convert all old boolean tab context menu preferences to separate JSON
   * strings for Firefox and TabMix items with shortened IDs
   *
   * @param {boolean} isFirstRun - Whether this is the first run after migration
   * @returns {Object} Object containing the JSON strings for Firefox and TabMix
   *   items
   */
  migrateTabContextPrefs(isFirstRun = false) {
    const tabmixPrefs = Services.prefs.getBranch("extensions.tabmix.");
    const hiddenFirefoxItems = [];
    const hiddenTabmixItems = [];

    // Check if tabContextMenu preference already exists
    const hasTabContextPref =
      tabmixPrefs.prefHasUserValue("firefoxContextMenu") ||
      tabmixPrefs.prefHasUserValue("tabmixContextMenu");

    // Process existing user preferences
    for (const [pref, id] of TAB_CONTEXT_MAPPING) {
      try {
        if (tabmixPrefs.prefHasUserValue(pref)) {
          const isVisible = tabmixPrefs.getBoolPref(pref);

          // If preference is set to false, add to hidden items list
          if (!isVisible) {
            // Remove prefix to make the string shorter
            let shortId = id;
            if (id.startsWith("context_")) {
              shortId = id.substring(8); // Remove "context_" prefix
            } else if (id.startsWith("tm-")) {
              shortId = id.substring(3); // Remove "tm-" prefix
            }

            // Add to the appropriate list
            if (id.startsWith("context_") || id === "share-tab-url-item") {
              hiddenFirefoxItems.push(shortId);
            } else {
              hiddenTabmixItems.push(shortId);
            }
          }

          // Clear old preference after migration
          tabmixPrefs.clearUserPref(pref);
        } else if (isFirstRun) {
          // On first run, also check default preferences from tabmix.js
          // This requires checking the default branch
          const defaultBranch = Services.prefs.getDefaultBranch("extensions.tabmix.");
          try {
            const defaultValue = defaultBranch.getBoolPref(pref);
            if (!defaultValue) {
              // If default is false, add to hidden items
              let shortId = id;
              if (id.startsWith("context_")) {
                shortId = id.substring(8);
              } else if (id.startsWith("tm-")) {
                shortId = id.substring(3);
              }

              if (id.startsWith("context_") || id === "share-tab-url-item") {
                hiddenFirefoxItems.push(shortId);
              } else {
                hiddenTabmixItems.push(shortId);
              }
            }
          } catch (ex) {
            // Ignore if default pref doesn't exist
          }
        }
      } catch (ex) {
        console.error(`Error processing preference ${pref}:`, ex);
      }
    }

    // Convert lists to JSON strings and set the new preferences
    const firefoxJson = JSON.stringify(hiddenFirefoxItems);
    const tabmixJson = JSON.stringify(hiddenTabmixItems);

    tabmixPrefs.setCharPref("firefoxContextMenu", firefoxJson);
    tabmixPrefs.setCharPref("tabmixContextMenu", tabmixJson);

    return {firefoxJson, tabmixJson};
  },

  /**
   * Check if tab context menu migration is needed
   *
   * @returns {boolean} True if migration is needed
   */
  tabContextMigrationNeeded() {
    const tabmixPrefs = Services.prefs.getBranch("extensions.tabmix.");

    // Check if any old preferences exist that need migration
    return TAB_CONTEXT_MAPPING.some(([pref]) => {
      try {
        return tabmixPrefs.prefHasUserValue(pref);
      } catch {
        return false;
      }
    });
  },

  /** Run all preference migrations as needed */
  runAllMigrations() {
    // For tab context menu, we need to know if this is the first run
    const tabmixPrefs = Services.prefs.getBranch("extensions.tabmix.");
    const isFirstRun =
      !tabmixPrefs.prefHasUserValue("firefoxContextMenu") &&
      !tabmixPrefs.prefHasUserValue("tabmixContextMenu");

    if (this.tabContextMigrationNeeded()) {
      this.migrateTabContextPrefs(isFirstRun);
    }

    // Add other migrations here as needed
    // this.migrateOtherPrefs();
  },
};
