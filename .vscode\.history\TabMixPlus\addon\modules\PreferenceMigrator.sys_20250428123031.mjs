/* eslint-disable @stylistic/lines-around-comment */

/**
 * Mo<PERSON><PERSON> to handle migration of old preferences to new formats Runs during
 * browser startup or when importing saved preference files
 */

/**
 * Mapping of old tab context menu preferences to new format Organized by
 * namespace for easier validation and lookup
 *
 * @type {{
 *   firefox: {[id: string]: string};
 *   tabmix: {[id: string]: string};
 *   defaultHidden: {firefox: string[]; tabmix: string[]};
 * }}
 */
const TAB_CONTEXT_MAPPING = {
  firefox: {
    bookmarkAllTabs: "bookmarkTabsMenu",
    bookmarkTab: "bookmarkTabMenu",
    closeTab: "closeTabMenu",
    closeTabOptions: "closeTabOptions",
    closeDuplicateTabs: "closeDuplicateTabs",
    openTabInWindow: "detachTabMenu",
    duplicateTab: "duplicateMenu",
    moveTabOptions: "moveTabOptions",
    toggleMuteTab: "muteTabMenu",
    openANewTab: "newTabMenu",
    pinTab: "pinTabMenu",
    reloadTab: "reloadTabMenu",
    reloadTabOptions: "reloadTabOptions",
    reopenInContainer: "reopenInContainer",
    selectAllTabs: "selectAllTabs",
    sendTabToDevice: "sendTabToDevice",
    shareTabURL: "shareTabURL",
    undoCloseTab: "undoCloseTabMenu",
  },
  tabmix: {
    autoreloadTab_menu: "autoReloadMenu",
    copyTabUrl: "copyTabUrlMenu",
    docShell: "docShellMenu",
    duplicateinWin: "duplicateinWinMenu",
    freezeTab: "freezeTabMenu",
    lockTab: "lockTabMenu",
    protectTab: "protectTabMenu",
    renameTab: "renameTabMenu",
    mergeWindowsTab: "showMergeWindow",
    undoCloseList: "undoCloseListMenu",
  },
  defaultHidden: {
    firefox: ["openTabInWindow"],
    tabmix: [
      "autoreloadTab_menu",
      "docShell",
      "duplicateinWin",
      "freezeTab",
      "renameTab",
      "mergeWindowsTab",
    ],
  },
};

export const PreferenceMigrator = {
  /**
   * Validates the tabContextMenu preference and returns the current state
   *
   * @returns {{
   *   isFirstRun: boolean;
   *   hiddenItems: {firefox: string[]; tabmix: string[]};
   * }}
   */
  validateTabContextMenu() {
    const tabmixPrefs = Services.prefs.getBranch("extensions.tabmix.");
    let isFirstRun = !tabmixPrefs.prefHasUserValue("tabContextMenu");
    /** @type {{firefox: string[]; tabmix: string[]}} */
    let hiddenItems = {firefox: [], tabmix: []};

    if (isFirstRun) {
      // For first run, use the default hidden items
      return {
        isFirstRun,
        hiddenItems: JSON.parse(JSON.stringify(TAB_CONTEXT_MAPPING.defaultHidden)),
      };
    }

    // If not first run, try to get existing hidden items and validate them
    try {
      const json = tabmixPrefs.getStringPref("tabContextMenu");
      /** @type {{firefox: string[]; tabmix: string[]}} */
      const tempItems = JSON.parse(json);

      // Validate the structure has expected keys
      if (
        tempItems &&
        typeof tempItems === "object" &&
        Array.isArray(tempItems.firefox) &&
        Array.isArray(tempItems.tabmix)
      ) {
        // Filter out invalid items
        hiddenItems.firefox = tempItems.firefox.filter(id => id in TAB_CONTEXT_MAPPING.firefox);
        hiddenItems.tabmix = tempItems.tabmix.filter(id => id in TAB_CONTEXT_MAPPING.tabmix);
        return {isFirstRun, hiddenItems};
      }
    } catch (ex) {
      console.error("Tabmix Error: Invalid tabContextMenu preference", ex);
    }

    // If we get here, there was an error parsing or validating the preference
    console.error("Tabmix Error: Invalid tabContextMenu structure, treating as first run");
    return {
      isFirstRun: true,
      hiddenItems: JSON.parse(JSON.stringify(TAB_CONTEXT_MAPPING.defaultHidden)),
    };
  },

  /**
   * Convert all old boolean tab context menu preferences to a single JSON
   * string with shortened IDs
   */
  migrateTabContextPrefs() {
    const tabmixPrefs = Services.prefs.getBranch("extensions.tabmix.");
    /**
     * @type {{
     *   isFirstRun: boolean;
     *   hiddenItems: {firefox: string[]; tabmix: string[]};
     * }}
     */
    const {isFirstRun, hiddenItems} = this.validateTabContextMenu();
    let migrationNeeded = false;

    // Process existing user preferences for Firefox items
    for (const [id, oldPref] of Object.entries(TAB_CONTEXT_MAPPING.firefox)) {
      try {
        if (tabmixPrefs.prefHasUserValue(oldPref)) {
          migrationNeeded = true;
          const isVisible = tabmixPrefs.getBoolPref(oldPref);

          // If preference is set to false, add to hidden items list
          if (!isVisible) {
            hiddenItems.firefox.push(id);
          }

          // Clear old preference after migration
          tabmixPrefs.clearUserPref(oldPref);
        } else if (isFirstRun && TAB_CONTEXT_MAPPING.defaultHidden.firefox.includes(id)) {
          // On first run, add items that should be hidden by default
          hiddenItems.firefox.push(id);
        }
      } catch (ex) {
        console.error(`Error processing Firefox preference ${oldPref}:`, ex);
      }
    }

    // Process existing user preferences for Tabmix items
    for (const [id, oldPref] of Object.entries(TAB_CONTEXT_MAPPING.tabmix)) {
      try {
        if (tabmixPrefs.prefHasUserValue(oldPref)) {
          migrationNeeded = true;
          const isVisible = tabmixPrefs.getBoolPref(oldPref);

          // If preference is set to false, add to hidden items list
          if (!isVisible) {
            hiddenItems.tabmix.push(id);
          }

          // Clear old preference after migration
          tabmixPrefs.clearUserPref(oldPref);
        } else if (isFirstRun && TAB_CONTEXT_MAPPING.defaultHidden.tabmix.includes(id)) {
          // On first run, add items that should be hidden by default
          hiddenItems.tabmix.push(id);
        }
      } catch (ex) {
        console.error(`Error processing Tabmix preference ${oldPref}:`, ex);
      }
    }

    if (migrationNeeded || isFirstRun) {
      tabmixPrefs.setStringPref("tabContextMenu", JSON.stringify(hiddenItems));
    }
  },

  /** Run all preference migrations as needed */
  runAllMigrations() {
    this.migrateTabContextPrefs();

    // Add other migrations here as needed
    // this.migrateOtherPrefs();
  },
};
