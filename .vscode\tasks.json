{
  "version": "2.0.0",
  "tasks": [
    // {
    //   "type": "eslint",
    //   "problemMatcher": [
    //     "$eslint-stylish"
    //   ],
    //   "label": "eslint: lint whole folder",
    //   "icon": {
    //     "dark": "${workspaceRoot}/icons/dark/eslint.svg",
    //     "light": "${workspaceRoot}/icons/light/eslint.svg"
    //   }
    // },
    // {
    //   "type": "shell",
    //   "group": "none",
    //   "command": "eslint --format stylish --rule \"tabmix/use-mjs-modules: error\" -c '${workspaceRoot}/eslint.config.js'",
    //   "windows": {
    //     "command": "eslint --format stylish --rule \"tabmix/use-mjs-modules: error\" -c '${workspaceRoot}/eslint.config.js'"
    //   },
    //   "label": "eslint: lint 'use-mjs-modules' rule",
    //   "problemMatcher": ["$eslint-stylish"],
    //   "presentation": {
    //     "reveal": "never",
    //     "close": true
    //   },
    //   "icon": {
    //     "dark": "${workspaceRoot}/icons/dark/eslint.svg",
    //     "light": "${workspaceRoot}/icons/light/eslint.svg"
    //   }
    // },
    {
      // force linting all open files by changing the date of
      "type": "shell",
      "group": "none",
      "command": "pwsh -Command (get-item '${workspaceRoot}/config/eslint.config.js').LastWriteTime = get-date",
      "windows": {
        "command": "pwsh  -Command (get-item '${workspaceRoot}/config/eslint.config.js').LastWriteTime = get-date"
      },
      "label": "eslint: lint opened files",
      "presentation": {
        "reveal": "never",
        "close": true
      },
      "icon": {
        "dark": "${workspaceRoot}/icons/dark/eslint.svg",
        "light": "${workspaceRoot}/icons/light/eslint.svg"
      }
    }
  ]
}
