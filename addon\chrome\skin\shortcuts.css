#shortcut-group {
  grid-auto-rows: max-content;
}

shortcut {
  flex-direction: column;
}

.shortcut-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 24px;
  margin: 1px 0;
  width: 100%;
}

.input-container {
  display: flex;
  align-items: center;
  border: 1px solid #b1b1b1;
  border-radius: 4px;
  box-sizing: inherit;
  font-weight: normal;
  height: 24px;
  padding: 2px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  width: 175px;
}

shortcut[focused="true"] .input-container {
  border-color: #0061e0;
  box-shadow: inset 0 0 0 1px rgb(0 97 224 / 75%);
}

.input-container[disabled] {
  opacity: 0.4;
}

.shortcut-edit-box {
  background-color: transparent;
  color: inherit;
  border: none;
  flex: 1;
  margin: 0;
  outline: none;
  padding: 0 0 0 4px;
}

.shortcut-image {
  opacity: 0.75;
  cursor: default;
  width: 16px;
  height: 16px;
  -moz-context-properties: stroke;
  z-index: 2;
  position: relative;
}

.shortcut-image:hover {
  opacity: 0.85;
}

shortcut:not([focused="true"]) .shortcut-image {
  visibility: collapse;
}

.shortcut-edit-box:not(:focus)::placeholder {
  color: transparent;
}

.shortcut-image:hover:active {
  opacity: 1;
}

.shortcut-image[anonid="disable"] {
  background-image: url("chrome://tabmixplus/skin/disabled.svg");
  stroke: red;
}

.shortcut-image[anonid="shortcut_reset"] {
  background-image: url("chrome://tabmixplus/skin/refresh.svg");
  margin-inline-end: 2px;
  stroke: black;
}

.shortcut-image[_hidden="true"],
shortcut[default="true"] .shortcut-image[anonid="shortcut_reset"],
shortcut[value=""] .shortcut-image[anonid="disable"],
shortcut:not([value]) .shortcut-image[anonid="disable"] {
  visibility: collapse;
}

@media (prefers-color-scheme: dark) {
  .shortcut-image[anonid="shortcut_reset"] {
    stroke: var(--shortcut-image-stroke, black);
  }
}

#TabMIxPreferences[linux] .shortcut-image[anonid="shortcut_reset"] {
  margin: 1px 0;
}

.shortcut-notificationbox,
.shortcut-warning-container,
shortcut[used="true"] > .shortcut-content {
  color: rgb(145 109 21);
}

.shortcut-notificationbox,
.shortcut-warning-container,
shortcut[used="true"] > .shortcut-content,
shortcut[used="true"] > .shortcut-content > .shortcut-edit-box > .shortcut-image,
shortcut[used="true"] > .shortcut-content > .shortcut-edit-box > * > .textbox-input {
  /* yellow */
  background-color: rgb(255 255 0 / 10%);
}

.shortcut-notificationbox {
  border-bottom: 2px solid;
  border-bottom-color: rgb(28 31 37 / 10%);
}

.shortcut-notificationbox:empty,
#shortcuts-panel[hide-shortcut-warning="true"]
  shortcut:not(:focus-within) > .shortcut-notificationbox {
  display: none;
}

#shortcuts-panel[usedKeys="false"] > .shortcut-warning-container {
  visibility: hidden;
}

.warning-icon {
  list-style-image: url("chrome://devtools/skin/images/alert.svg");
  width: 16px;
  height: 15px;
  margin: 3px 0;
}

.shortcut-content > * > .text-link {
  margin-bottom: 0;
}

#shortcuts-panel[hide-unused-shortcuts="true"] shortcut:not([value]) + hbox,
#shortcuts-panel[hide-unused-shortcuts="true"] shortcut:not([value]) {
  visibility: collapse;
}

description {
  margin-block: 0;
}

.shortcut-notificationbox > description:not(:first-child) {
  padding-inline-start: 1.5rem;
}

description[disabled] {
  color: GrayText;
}
