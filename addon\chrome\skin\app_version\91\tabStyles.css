/* stylelint-disable no-descending-specificity */

/**
    Firefox version 78+
    Bug 1066531 replaced selected attribute with visuallyselected

    :::: use visuallyselected instead of selected ::::

    ALL platform

**/

/* :::: For current, unloaded, unread and other tab styles :::: */
#tabbrowser-tabs[tabmix_currentStyle~="bold"] > #tabbrowser-arrowscrollbox >
    .tabbrowser-tab[visuallyselected="true"] .tab-text,
#tabbrowser-tabs[tabmix_otherStyle~="bold"] > #tabbrowser-arrowscrollbox >
    .tabbrowser-tab:not([tabmix_tabState], [visuallyselected="true"]) .tab-text,
#tabbrowser-tabs[tabmix_unloadedStyle~="bold"] > #tabbrowser-arrowscrollbox >
    .tabbrowser-tab:not([visuallyselected="true"])[tabmix_tabState="unloaded"] .tab-text,
#tabbrowser-tabs[tabmix_unreadStyle~="bold"] > #tabbrowser-arrowscrollbox >
    .tabbrowser-tab:not([visuallyselected="true"])[tabmix_tabState="unread"] .tab-text {
  font-weight: bold !important;
}

#tabbrowser-tabs[tabmix_currentStyle~="not-bold"] > #tabbrowser-arrowscrollbox >
    .tabbrowser-tab[visuallyselected="true"] .tab-text,
#tabbrowser-tabs[tabmix_otherStyle~="not-bold"] > #tabbrowser-arrowscrollbox >
    .tabbrowser-tab:not([tabmix_tabState], [visuallyselected="true"]) .tab-text,
#tabbrowser-tabs[tabmix_unloadedStyle~="not-bold"] > #tabbrowser-arrowscrollbox >
    .tabbrowser-tab:not([visuallyselected="true"])[tabmix_tabState="unloaded"] .tab-text,
#tabbrowser-tabs[tabmix_unreadStyle~="not-bold"] > #tabbrowser-arrowscrollbox >
    .tabbrowser-tab:not([visuallyselected="true"])[tabmix_tabState="unread"] .tab-text {
  font-weight: normal !important;
}

#tabbrowser-tabs[tabmix_currentStyle~="italic"] > #tabbrowser-arrowscrollbox >
    .tabbrowser-tab[visuallyselected="true"] .tab-text,
#tabbrowser-tabs[tabmix_otherStyle~="italic"] > #tabbrowser-arrowscrollbox >
    .tabbrowser-tab:not([tabmix_tabState], [visuallyselected="true"]) .tab-text,
#tabbrowser-tabs[tabmix_unloadedStyle~="italic"] > #tabbrowser-arrowscrollbox >
    .tabbrowser-tab:not([visuallyselected="true"])[tabmix_tabState="unloaded"] .tab-text,
#tabbrowser-tabs[tabmix_unreadStyle~="italic"] > #tabbrowser-arrowscrollbox >
    .tabbrowser-tab:not([visuallyselected="true"])[tabmix_tabState="unread"] .tab-text {
  font-style: italic !important;
}

#tabbrowser-tabs[tabmix_currentStyle~="not-italic"] > #tabbrowser-arrowscrollbox >
    .tabbrowser-tab[visuallyselected="true"] .tab-text,
#tabbrowser-tabs[tabmix_otherStyle~="not-italic"] > #tabbrowser-arrowscrollbox >
    .tabbrowser-tab:not([tabmix_tabState], [visuallyselected="true"]) .tab-text,
#tabbrowser-tabs[tabmix_unloadedStyle~="not-italic"] > #tabbrowser-arrowscrollbox >
    .tabbrowser-tab:not([visuallyselected="true"])[tabmix_tabState="unloaded"] .tab-text,
#tabbrowser-tabs[tabmix_unreadStyle~="not-italic"] > #tabbrowser-arrowscrollbox >
    .tabbrowser-tab:not([visuallyselected="true"])[tabmix_tabState="unread"] .tab-text {
  font-style: normal !important;
}

#tabbrowser-tabs[tabmix_currentStyle~="underline"] > #tabbrowser-arrowscrollbox >
    .tabbrowser-tab[visuallyselected="true"] .tab-text,
#tabbrowser-tabs[tabmix_otherStyle~="underline"] > #tabbrowser-arrowscrollbox >
    .tabbrowser-tab:not([tabmix_tabState], [visuallyselected="true"]) .tab-text,
#tabbrowser-tabs[tabmix_unloadedStyle~="underline"] > #tabbrowser-arrowscrollbox >
    .tabbrowser-tab:not([visuallyselected="true"])[tabmix_tabState="unloaded"] .tab-text,
#tabbrowser-tabs[tabmix_unreadStyle~="underline"] > #tabbrowser-arrowscrollbox >
    .tabbrowser-tab:not([visuallyselected="true"])[tabmix_tabState="unread"] .tab-text {
  text-decoration: underline !important;
}
