/**
    Firefox version 78+
    Bug 1066531 replaced selected attribute with visuallyselected

    :::: use visuallyselected instead of selected ::::

    Windows platform

**/

/* Override Windows 10 rules */
#tabbrowser-tabs[tabmix_currentStyle~="bg"] > #tabbrowser-arrowscrollbox > .tabbrowser-tab[visuallyselected="true"] > .tab-stack > .tab-background > .tab-background-middle {
  background-size: auto 100%, auto 100%, auto 100%;
  background-clip: content-box;
}

#tabbrowser-tabs[tabmix-multibar="true"] #tabbrowser-arrowscrollbox > toolbarbutton {
  vertical-align: bottom;
}

#tabmix-bottom-toolbox > toolbox  {
  appearance: none;
  border-top: none;
  background-color: transparent;
}

#TabsToolbar[tabbaronbottom]:not(:-moz-lwtheme) {
  appearance: none;
}

#TabsToolbar[tabbaronbottom]::before,
#TabsToolbar[tabbaronbottom]::after {
  box-shadow: none !important;
}

#tabbrowser-tabs:not([treestyletab-mode="vertical"])
    > #tabbrowser-arrowscrollbox > .tabbrowser-tab > .tab-stack > .tab-progress-container > .tab-progress {
  margin: 2px;
}
