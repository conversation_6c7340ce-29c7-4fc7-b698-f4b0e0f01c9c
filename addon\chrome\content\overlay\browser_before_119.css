/* stylelint-disable no-descending-specificity */

/**
 * xml-stylesheet for tabmix.xul
 *
 * This file is for general use and intended for ALL Firefox version and Platform
 * if some rule become obsolete move it to the proper file
 *
 **/

/*  we don't use .tabbrowser-tab > .tab-close-button here
    because in some theme the buttons are inside tab-middle
*/

/* use both selected and not selected to override specific rules from other extensions */
#tabbrowser-tabs[closebuttons="noclose"] > #tabbrowser-arrowscrollbox > .tabbrowser-tab:not([selected="true"]) .tab-close-button,
#tabbrowser-tabs[closebuttons="noclose"] > #tabbrowser-arrowscrollbox > .tabbrowser-tab[selected="true"] .tab-close-button {
  display: none !important;
}

.tabbrowser-tab[protected] .tab-close-button {
  display: none !important;
}

#tabbrowser-tabs[favhideclose="true"] > #tabbrowser-arrowscrollbox > .tabbrowser-tab[faviconized="true"] .tab-close-button {
   display: none !important;
}

#tabbrowser-tabs:not([favhideclose="true"])[closebuttons="activetab"] > #tabbrowser-arrowscrollbox > .tabbrowser-tab:not([pinned], [isPermaTab="true"], [protected])[selected="true"] .tab-close-button,
#tabbrowser-tabs:not([favhideclose="true"])[closebuttons="alltabs"] > #tabbrowser-arrowscrollbox > .tabbrowser-tab:not([pinned], [isPermaTab="true"], [protected]) .tab-close-button {
  display: inline-flex !important;
}

#tabbrowser-tabs[favhideclose="true"][closebuttons="activetab"] > #tabbrowser-arrowscrollbox > .tabbrowser-tab:not([pinned], [faviconized="true"], [isPermaTab="true"], [protected])[selected="true"] .tab-close-button,
#tabbrowser-tabs[favhideclose="true"][closebuttons="alltabs"] > #tabbrowser-arrowscrollbox > .tabbrowser-tab:not([pinned], [faviconized="true"], [isPermaTab="true"], [protected]) .tab-close-button {
  display: inline-flex !important;
}

#tabbrowser-tabs[closebuttons-hover="notactivetab"] > #tabbrowser-arrowscrollbox > .tabbrowser-tab:not([pinned], [faviconized="true"], [selected="true"], [isPermaTab="true"], [protected])[showbutton="on"] .tab-close-button,
#tabbrowser-tabs[closebuttons-hover="alltabs"] > #tabbrowser-arrowscrollbox > .tabbrowser-tab:not([pinned], [faviconized="true"], [isPermaTab="true"], [protected])[showbutton="on"] .tab-close-button {
  display: inline-flex !important;
}

/* button on the left side */
#tabbrowser-tabs[closebuttons-side="left"][closebuttons-hover="notactivetab"] > #tabbrowser-arrowscrollbox > .tabbrowser-tab:not([selected="true"]) > .tab-stack > .tab-content > .tab-icon,
#tabbrowser-tabs[closebuttons-side="left"][closebuttons-hover="alltabs"] > #tabbrowser-arrowscrollbox > .tabbrowser-tab > .tab-stack > .tab-content > .tab-icon {
  order: 1 !important;
}

#tabbrowser-tabs[closebuttons-side="left"][closebuttons="activetab"] > #tabbrowser-arrowscrollbox > .tabbrowser-tab[selected="true"] > .tab-stack > .tab-content > .tab-icon,
#tabbrowser-tabs[closebuttons-side="left"][closebuttons-hover="notactivetab"] > #tabbrowser-arrowscrollbox > .tabbrowser-tab:not([selected="true"]) > .tab-stack > .tab-content > .tab-close-button,
#tabbrowser-tabs[closebuttons-side="left"][closebuttons-hover="alltabs"] > #tabbrowser-arrowscrollbox > .tabbrowser-tab > .tab-stack > .tab-content > .tab-close-button {
  order: 2 !important;
}

#tabbrowser-tabs[closebuttons-side="left"][closebuttons-hover="notactivetab"] > #tabbrowser-arrowscrollbox > .tabbrowser-tab:not([selected="true"]) > .tab-stack > .tab-content > .tab-sharing-icon-overlay,
#tabbrowser-tabs[closebuttons-side="left"][closebuttons-hover="alltabs"] > #tabbrowser-arrowscrollbox > .tabbrowser-tab > .tab-stack > .tab-content > .tab-sharing-icon-overlay {
  order: 1 !important;
}

#tabbrowser-tabs[closebuttons-side="left"][closebuttons-hover="notactivetab"] > #tabbrowser-arrowscrollbox > .tabbrowser-tab:not([selected="true"]) > .tab-stack > .tab-content > .tab-icon-overlay,
#tabbrowser-tabs[closebuttons-side="left"][closebuttons-hover="alltabs"] > #tabbrowser-arrowscrollbox > .tabbrowser-tab > .tab-stack > .tab-content > .tab-icon-overlay {
  order: 1 !important;
}

#tabbrowser-tabs[closebuttons-side="left"][closebuttons-hover="notactivetab"] > #tabbrowser-arrowscrollbox > .tabbrowser-tab:not([selected="true"]) > .tab-stack > .tab-content > .tab-label-container,
#tabbrowser-tabs[closebuttons-side="left"][closebuttons-hover="alltabs"] > #tabbrowser-arrowscrollbox > .tabbrowser-tab > .tab-stack > .tab-content > .tab-label-container,
#tabbrowser-tabs[closebuttons-side="left"][closebuttons-hover="notactivetab"] > #tabbrowser-arrowscrollbox > .tabbrowser-tab:not([selected="true"]) > .tab-stack > .tab-content > .tab-label,
#tabbrowser-tabs[closebuttons-side="left"][closebuttons-hover="alltabs"] > #tabbrowser-arrowscrollbox > .tabbrowser-tab > .tab-stack > .tab-content > .tab-label {
  order: 3 !important;
  pointer-events: none;
}

#tabbrowser-tabs[closebuttons-side="left"][closebuttons-hover="notactivetab"] > #tabbrowser-arrowscrollbox > .tabbrowser-tab:not([selected="true"]) > .tab-stack > .tab-content > .tab-icon-sound,
#tabbrowser-tabs[closebuttons-side="left"][closebuttons-hover="alltabs"] > #tabbrowser-arrowscrollbox > .tabbrowser-tab > .tab-stack > .tab-content > .tab-icon-sound {
  order: 3 !important;
}

#tabbrowser-tabs:not([closebuttons="noclose"], [closebuttons-hover])[closebuttons-side="left"] > #tabbrowser-arrowscrollbox > .tabbrowser-tab > .tab-stack > .tab-content > *:not(.tab-close-button) {
  order: 5 !important;
}

#tabbrowser-tabs:not([closebuttons-side="left"]) > #tabbrowser-arrowscrollbox > .tabbrowser-tab > .tab-stack > .tab-content > *:not(.tab-close-button, .tab-icon-sound, .tab-label, .tab-label-container, label) {
  order: 1;
}

#tabbrowser-tabs:not([closebuttons-side="left"]) > #tabbrowser-arrowscrollbox > .tabbrowser-tab > .tab-stack > .tab-content > .tab-label-container,
#tabbrowser-tabs:not([closebuttons-side="left"]) > #tabbrowser-arrowscrollbox > .tabbrowser-tab > .tab-stack > .tab-content > .tab-label {
  order: 2;
}

#tabbrowser-tabs:not([closebuttons-side="left"]) > #tabbrowser-arrowscrollbox > .tabbrowser-tab > .tab-stack > .tab-content > .tab-icon-sound {
  order: 3;
}

#tabbrowser-tabs:not([closebuttons-side="left"]) > #tabbrowser-arrowscrollbox > .tabbrowser-tab > .tab-stack > .tab-content > label {
  order: 4;
}

#tabbrowser-tabs:not([closebuttons-side="left"]) > #tabbrowser-arrowscrollbox > .tabbrowser-tab > .tab-stack > .tab-content > .tab-close-button {
  order: 5;
}

#tabbrowser-tabs[closebuttons-side="left"][closebuttons-hover="notactivetab"] > #tabbrowser-arrowscrollbox > .tabbrowser-tab[selected="true"] > .tab-stack > .tab-content > .tab-close-button,
#tabbrowser-tabs:not([closebuttons="noclose"], [closebuttons-hover])[closebuttons-side="left"] > #tabbrowser-arrowscrollbox > .tabbrowser-tab > .tab-stack > .tab-content > .tab-close-button {
  direction: rtl;
  order: 0 !important;
}

/* for themes that use old tabmix xml version */
.tab-close-button[button_side="left"],
.tabbrowser-tab .showhover-box,
.tabbrowser-tab .showhover-flex {
  display: none !important;
}

/* we use this rule in tabmix.js Tabmix.getAfterTabsButtonsWidth */
#tabbrowser-arrowscrollbox > #tabs-newtab-button[force-display],
#tabbrowser-arrowscrollbox > #tabbrowser-arrowscrollbox-periphery > #tabs-newtab-button[force-display] {
  display: flex !important;
  align-items: center;
}

/*** hide tabs-newtab-button that we don't use ***/
#TabsToolbar:not([currentset*="privateTab-toolbar-openNewPrivateTab"]) #privateTab-afterTabs-openNewPrivateTab,
#tabbrowser-tabs[overflow="true"] > #tabbrowser-arrowscrollbox > #tabs-newtab-button,
#TabsToolbar:not([tabmix-show-newtabbutton]) #tabbrowser-arrowscrollbox > #tabs-newtab-button[command="cmd_newNavigatorTab"],
#TabsToolbar[tabmix-show-newtabbutton*="side"] #tabbrowser-arrowscrollbox > #tabs-newtab-button,
#tabbrowser-tabs[overflow="true"] > #tabbrowser-arrowscrollbox > #tabbrowser-arrowscrollbox-periphery > #tabs-newtab-button,
#TabsToolbar:not([tabmix-show-newtabbutton]) #tabbrowser-arrowscrollbox > #tabbrowser-arrowscrollbox-periphery > #tabs-newtab-button[command="cmd_newNavigatorTab"],
#TabsToolbar[tabmix-show-newtabbutton*="side"] #tabbrowser-arrowscrollbox > #tabbrowser-arrowscrollbox-periphery > #tabs-newtab-button {
  display: none;
  border: none;
  margin: 0;
  vertical-align: bottom;
}

#TabsToolbar[tabmix-show-newtabbutton="aftertabs"]:not([customizing="true"])
  #tabbrowser-tabs:not([overflow="true"]) > #tabbrowser-arrowscrollbox > #tabs-newtab-button[command="cmd_newNavigatorTab"],
#TabsToolbar[tabmix-show-newtabbutton="aftertabs"]:not([customizing="true"])
  #tabbrowser-tabs:not([overflow="true"]) > #tabbrowser-arrowscrollbox > #tabbrowser-arrowscrollbox-periphery > #tabs-newtab-button[command="cmd_newNavigatorTab"] {
  display: flex;
  align-items: center;
  visibility: visible;
}

/* override firefox rule from browser.css */
#tabbrowser-tabs[hasadjacentnewtabbutton]:not([overflow="true"]) ~ #new-tab-button {
  display: flex;
  align-items: center;
}

#TabsToolbar[tabmix-show-newtabbutton*="side"] #tabbrowser-tabs[hasadjacentnewtabbutton]:not([overflow="true"]) ~ #new-tab-button,
#TabsToolbar[tabmix-show-newtabbutton="aftertabs"] #tabbrowser-tabs[overflow="true"] ~ #new-tab-button,
#TabsToolbar[tabmix-show-newtabbutton*="side"] #new-tab-button {
  visibility: visible;
}

#TabsToolbar:not([customizing])[tabmix-show-newtabbutton*="aftertabs"]  #new-tab-button,
#TabsToolbar:not([tabmix-show-newtabbutton])  #new-tab-button {
  visibility: collapse;
}

/*** Private-tab compatibility ***/
#privateTab-afterTabs-openNewPrivateTab {
  vertical-align: bottom;
}

#TabsToolbar[currentset*="privateTab-toolbar-openNewPrivateTab"]:not([tabmix-show-newtabbutton])
  #tabbrowser-tabs:not([overflow="true"]) > #tabbrowser-arrowscrollbox > #tabs-newtab-button[command="cmd_newNavigatorTab"],
#TabsToolbar[currentset*="privateTab-toolbar-openNewPrivateTab"]:not([tabmix-show-newtabbutton])
  #tabbrowser-tabs:not([overflow="true"]) > #tabbrowser-arrowscrollbox > #tabbrowser-arrowscrollbox-periphery > #tabs-newtab-button[command="cmd_newNavigatorTab"] {
  display: none; /* override privateTab visibility: visible !important; */
}

#TabsToolbar[currentset*="privateTab-toolbar-openNewPrivateTab"][tabmix-show-newtabbutton*="side"]
  #tabbrowser-tabs:not([overflow="true"]) > #tabbrowser-arrowscrollbox > #tabs-newtab-button,
#TabsToolbar[currentset*="privateTab-toolbar-openNewPrivateTab"][tabmix-show-newtabbutton*="side"]
  #tabbrowser-tabs:not([overflow="true"]) > #tabbrowser-arrowscrollbox > #tabbrowser-arrowscrollbox-periphery > #tabs-newtab-button {
  display: none; /* override privateTab visibility: visible !important; */
}

#TabsToolbar[currentset*="privateTab-toolbar-openNewPrivateTab"][tabmix-show-newtabbutton*="aftertabs"]
     #privateTab-toolbar-openNewPrivateTab {
  visibility: collapse; /* missing rule form privateTab */
}

#TabsToolbar[currentset*="privateTab-toolbar-openNewPrivateTab"][tabmix-show-newtabbutton*="aftertabs"]:not([customizing="true"])
     #tabbrowser-tabs > #tabbrowser-arrowscrollbox > #privateTab-afterTabs-openNewPrivateTab,
#TabsToolbar[tabmix-show-newtabbutton*="side"] > #privateTab-toolbar-openNewPrivateTab {
  visibility: visible;
}

/***  Tab opening animation  ***/
.tab-progress-container > .tab-progress:not([fadein], [pinned]),
.tab-icon:not([fadein], [pinned]) > .tab-protect-icon,
.tab-icon:not([fadein], [pinned]) > .tab-lock-icon,
.tab-icon:not([fadein], [pinned]) > .tab-reload-icon {
   display: none !important;
}

/* we use display: inline-flex !important;
so display: none !important; does not hide the button */
.tab-close-button:not([fadein]) {
  visibility: hidden;
}

/* we don't remove top and bottom borders to prevent tab height from shrinking */
.tabbrowser-tab:not([fadein], [pinned]) {
  border-left: none;
  border-right: none;
}

/*** backward compatibility with theme that still use old tabmix tab binding ***/
.tabbrowser-tab:not([fadein], [pinned]) .tab-text-stack > .tab-progress {
  display: none !important;
}

/***  For progressmeter on tab Firefox 4.0+  ***/
.tab-progress {
  margin: 0;
  min-width: 0;
}

.tabbrowser-tab > .tab-stack > .tab-progress-container {
  pointer-events: none;
}

.tabbrowser-tab > .tab-stack > .tab-progress-container > .tab-progress {
  display: none;
}

#tabbrowser-tabs[tabmix_progressMeter] > #tabbrowser-arrowscrollbox > .tabbrowser-tab:not([pinned])[busy] > .tab-stack > .tab-progress-container > .tab-progress[value] {
  display: inline-block;
}

.tab-progress > .progress-bar:-moz-locale-dir(ltr) {
  background-image: linear-gradient(to right, rgb(255 255 255 / 10%) 50%,
                                        rgb(255 255 255 / 40%) 90%,
                                        rgb(255 255 255 / 80%));
}

.tab-progress > .progress-bar:-moz-locale-dir(rtl) {
  background-image: linear-gradient(to left, rgb(255 255 255 / 10%) 50%,
                                        rgb(255 255 255 / 40%) 90%,
                                        rgb(255 255 255 / 80%));
}

/* :::: widthFitTitle :::: */
#tabbrowser-tabs[orient="horizontal"][widthFitTitle] > #tabbrowser-arrowscrollbox > .tabbrowser-tab:not([pinned]) {
  flex: 0 0 auto !important;
  width: auto !important;
}

#tabbrowser-tabs[orient="horizontal"]:not([widthFitTitle]) > #tabbrowser-arrowscrollbox > .tabbrowser-tab:not([pinned], [tiletabs-single="hidden"]) {
  flex: 1 1 0% !important;
}

/* we add #TabsToolbar to get higher specificity, in order to override Firefox rule */
#TabsToolbar #tabbrowser-tabs:not([overflow="true"], [hashiddentabs], [showalltabsbutton]) ~ #alltabs-button {
  display: none;
}

#TabsToolbar #tabbrowser-tabs[showalltabsbutton] ~ #alltabs-button {
  display: flex;
  align-items: center;
}

#TabsToolbar #tabbrowser-tabs:not([overflow="true"], [showalltabsbutton])[using-closing-tabs-spacer] ~ #alltabs-button {
  /* temporary space to keep a tab's close button under the cursor */
  display: flex;
  visibility: hidden;
}

/* for the case user drag background tab when selectTabOnMouseDown is false */
#tabbrowser-tabs[tabmix-movingBackgroundTab] > #tabbrowser-arrowscrollbox > .tabbrowser-tab[tabmix-dragged] {
  position: relative;
  z-index: 3;
  pointer-events: none; /* avoid blocking dragover events on scroll buttons */
}

#tabbrowser-tabs[tabmix-movingBackgroundTab] > #tabbrowser-arrowscrollbox > .tabbrowser-tab:not([tabdrop-samewindow])[tabmix-dragged] {
  transition: none !important;
}

@media (prefers-reduced-motion: no-preference) {
  #tabbrowser-tabs[tabmix-movingBackgroundTab] > #tabbrowser-arrowscrollbox > .tabbrowser-tab[fadein]:not([tabmix-dragged]) {
    transition: transform 200ms var(--animation-easing-function, ease-out);
  }
}

#tabbrowser-arrowscrollbox[tabmix-flowing="multibar"] > .arrowscrollbox-overflow-start-indicator,
#tabbrowser-arrowscrollbox[tabmix-flowing="multibar"] > .arrowscrollbox-overflow-end-indicator {
  margin-bottom: 0;
  visibility: collapse;
}

#tabmix-rows-tooltip:not([tabmix-flowing="multibar"]) {
  display: none;
}

/* hide menuitem in tabContextMenu */
#tabContextMenu menu[tabmix_hide="true"],
#tabContextMenu menuitem[tabmix_hide="true"] {
  visibility: collapse;
}

/* :::: tabbar on bottom :::: */

/* we don't need rule for toolbar-drag - when tabbar on bottom */
#TabsToolbar[tabbaronbottom] {
  display: block;
  position: fixed !important;
  appearance: none !important;
  top: var(--tabmix-bottom-toolbox-top);
  width: 100%;

  /* look at browser.css for each platform
  background-image: none !important;
  */
}

#TabsToolbar[tabbaronbottom] > .toolbar-items {
  width: 100%;
}

#main-window:not([tabmix-tabbaronbottom]) #tabmix-bottom-toolbox {
  visibility: collapse;
}

/* it will trigger resize if we exit hidden tabbar mode before
   we set width or height to this box
*/
#tabmix-bottom-toolbox > toolbox {
  appearance: none;
  min-width: 1px;
  min-height: 1px;
  height: calc(var(--tab-min-height-mlt) * var(--tabmix-visiblerows, 1))
}

#tabmix-bottom-toolbox[tabmix-multibar] > toolbox {
  margin-block: var(--tabmix-multirow-margin , 0);
}

#tabmix-bottom-toolbox[fullscreenShouldAnimate] {
  transition: 0.8s margin-bottom ease-out;
}

#main-window[inFullscreen][inDOMFullscreen] #tabmix-bottom-toolbox,
#main-window[inFullscreen][inDOMFullscreen] #fullscr-bottom-toggler {
  visibility: collapse;
}

#main-window[tabmix-tabbaronbottom] #toolbar-menubar[autohide="true"][inactive="true"]:not([customizing="true"]) {
	min-height: initial !important;
	height: initial !important;
	appearance: initial !important;
}

#main-window[tabmix-tabbaronbottom] #toolbar-menubar[autohide="true"][inactive="true"]:not([customizing="true"]) > #menubar-items > * {
  opacity: 0;
}

/* :::: Fullscreen pseudo-toolbar :::: */
#fullscr-bottom-toggler {
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  position: fixed;
  z-index: 2147483647;
}
