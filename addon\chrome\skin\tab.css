/* stylelint-disable no-descending-specificity */

/**
    Firefox all versions

    for ALL platforms

**/

/* we need to make multirow work on all themes, this should help a bit */
#tabbrowser-tabs[tabmix-flowing="multibar"][tabmix-multibar],
#tabbrowser-tabs[tabmix-flowing="multibar"][tabmix-multibar] .tabbrowser-strip,
#tabbrowser-tabs:not([classic="v3"])[tabmix-flowing="multibar"][tabmix-multibar] .tabbrowser-tab,
#tabbrowser-tabs[tabmix-flowing="multibar"][tabmix-multibar] .tabs-left,
#tabbrowser-tabs[tabmix-flowing="multibar"][tabmix-multibar] .tabs-right {
  max-height: none !important;
}

#tabbrowser-tabs[tabmix-flowing="multibar"][tabmix-multibar="scrollbar"] .tabbrowser-tab[collapsed="true"] {
  display: none !important;
}

/* some theme align text to center this make all tab align to center in multi-rows */
#tabbrowser-tabs[tabmix-flowing="multibar"] {
  text-align: start !important;
}

/* fix background for multi-row */
#tabbrowser-tabs[tabmix-flowing="multibar"][backgroundrepeat] {
  background-repeat: repeat;
}

/* For Flowing tabs */
#tabbrowser-tabs[tabmix-flowing="multibar"] .tabbrowser-tab {
  vertical-align: bottom;
}

/* for extra icons */
.tab-icon-stack:not([pinned], [sharing], [crashed]):is([soundplaying], [muted], [activemedia-blocked]) > .tab-lock-icon {
  opacity: 1 !important;
  margin-inline-start: -4px;
}

.tab-icon-stack:not([pinned], [sharing], [crashed]):is([soundplaying], [muted], [activemedia-blocked]) > .tab-protect-icon,
.tab-icon-stack:not([pinned], [sharing], [crashed]):is([soundplaying], [muted], [activemedia-blocked]) > .tab-reload-icon {
  opacity: 1 !important;
  margin-inline-start: 4px;
}

.tab-protect-icon {
  list-style-image: url("protected.png");
  display: none;
  height: 16px;
  width: 16px;
}

.tab-lock-icon {
  list-style-image: url("locked.png");
  display: none;
  height: 16px;
  width: 16px;
}

.tab-reload-icon {
  list-style-image: url("autoreload.png");
  display: none;
  height: 16px;
  width: 16px;
}

#tabbrowser-tabs[tabmix_icons~="autoreload"] .tabbrowser-tab[_reload] .tab-reload-icon ,
#tabbrowser-tabs[tabmix_icons~="protected"] .tabbrowser-tab[protected] .tab-protect-icon ,
#tabbrowser-tabs[tabmix_icons~="locked"] .tabbrowser-tab[locked] .tab-lock-icon {
  display: inline-flex;
}

#tabbrowser-tabs[tabmix_icons~="notpinned"] .tabbrowser-tab[pinned] .tab-reload-icon,
#tabbrowser-tabs[tabmix_icons~="notpinned"] .tabbrowser-tab[pinned] .tab-protect-icon,
#tabbrowser-tabs[tabmix_icons~="notpinned"] .tabbrowser-tab[pinned] .tab-lock-icon {
  display: none;
}

/*  :::::: Tabmix toolbar button :::::: */
#tabmix-closedTabsButton { list-style-image: url("icons/closed-tabs-24x24.png"); }
#tabmix-closedTabsButton:hover { list-style-image: url("icons/closed-tabs-24x24-hovered.png"); }
#main-window:not([customizing]) #tabmix-closedTabsButton[disabled="true"] { list-style-image: url("icons/closed-tabs-24x24-disabled.png"); }

toolbar[iconsize="small"] #tabmix-closedTabsButton, .closedtabs-icon > :is(.menu-iconic-left, .menu-icon) { list-style-image: url("icons/closed-tabs-16x16.png"); }
toolbar[iconsize="small"] #tabmix-closedTabsButton:hover, .closedtabs-icon:hover > :is(.menu-iconic-left, .menu-icon) { list-style-image: url("icons/closed-tabs-16x16-hovered.png"); }
#main-window:not([customizing]) toolbar[iconsize="small"] #tabmix-closedTabsButton[disabled="true"], .closedtabs-icon[disabled="true"] > :is(.menu-iconic-left, .menu-icon) { list-style-image: url("icons/closed-tabs-16x16-disabled.png"); }

#tabmix-closedTabs-toolbaritem[cui-areatype="toolbar"] #tabmix-closedTabsButton[type="menu-button"] .toolbarbutton-icon {
  border-right: 1px solid rgb(0 0 0 / 16%);
  border-radius: 0;
  padding-inline-end: 3px;
  width: calc(1 * var(--toolbarbutton-inner-padding) + 16px + 3px);
}

#wrapper-tabmix-closedTabs-toolbaritem[place="palette"] > #tabmix-closedTabs-toolbaritem > #tabmix-closedTabsButton[type="menu-button"] {
  align-items: flex-end;
}

#wrapper-tabmix-closedTabs-toolbaritem[place="palette"] > #tabmix-closedTabs-toolbaritem > #tabmix-closedTabs-dropmarker[type="menu-button"] {
  align-items: flex-start;
}

#tabmix-closedTabs-toolbaritem[cui-areatype="menu-panel"] > #tabmix-closedTabs-dropmarker,
#tabmix-closedTabs-dropmarker:not([type="menu-button"]) {
  display: none;
}

#tabmix-closedWindowsButton { list-style-image: url("icons/closed-win-24x24.png"); }
#tabmix-closedWindowsButton:hover { list-style-image: url("icons/closed-win-24x24-hovered.png"); }
#main-window:not([customizing]) #tabmix-closedWindowsButton[disabled="true"] { list-style-image: url("icons/closed-win-24x24-disabled.png"); }
toolbar[iconsize="small"] #tabmix-closedWindowsButton, .closedwindows-icon > :is(.menu-iconic-left, .menu-icon) { list-style-image: url("icons/closed-win-16x16.png"); }
toolbar[iconsize="small"] #tabmix-closedWindowsButton:hover, .closedwindows-icon:hover > :is(.menu-iconic-left, .menu-icon) { list-style-image: url("icons/closed-win-16x16-hovered.png")!important; }
#main-window:not([customizing]) toolbar[iconsize="small"] #tabmix-closedWindowsButton[disabled="true"], .closedwindows-icon[disabled="true"] > :is(.menu-iconic-left, .menu-icon) { list-style-image: url("icons/closed-win-16x16-disabled.png")!important; }

#tabmix-alltabs-button { list-style-image: url("icons/tab-list-24x24.png")!important; }
#tabmix-alltabs-button:hover { list-style-image: url("icons/tab-list-24x24-hovered.png") }
#tabmix-alltabs-button[disabled="true"] { list-style-image: url("icons/tab-list-24x24-disabled.png"); }
toolbar[iconsize="small"] #tabmix-alltabs-button, .tabmix-tabslist-icon > :is(.menu-iconic-left, .menu-icon) { list-style-image: url("icons/tab-list-16x16.png"); }
toolbar[iconsize="small"] #tabmix-alltabs-button:hover, .tabmix-tabslist-icon:hover > :is(.menu-iconic-left, .menu-icon) { list-style-image: url("icons/tab-list-16x16-hovered.png"); }
toolbar[iconsize="small"] #tabmix-alltabs-button[disabled="true"], .tabmix-tabslist-icon[disabled="true"] > :is(.menu-iconic-left, .menu-icon) { list-style-image: url("icons/tab-list-24x24-disabled.png"); }

.tabmix-menu-icon :is(.menu-iconic-left, .menu-icon) { list-style-image: url("tmpsmall.png"); }
.tabmix-menu-icon .menu-icon { list-style-image: url("tmpsmall.png"); }

#btn_sessionmanager { list-style-image: url("icons/session-manager-24x24.png"); }
#btn_sessionmanager:hover { list-style-image: url("icons/session-manager-24x24-hovered.png"); }
#main-window:not([customizing]) #btn_sessionmanager[disabled="true"] { list-style-image: url("icons/session-manager-24x24-disabled.png"); }
toolbar[iconsize="small"] #btn_sessionmanager, .sessionmanager-icon > :is(.menu-iconic-left, .menu-icon) { list-style-image: url("icons/session-manager-16x16.png"); }
toolbar[iconsize="small"] #btn_sessionmanager:hover, .sessionmanager-icon:hover > :is(.menu-iconic-left, .menu-icon) { list-style-image: url("icons/session-manager-16x16-hovered.png"); }
#main-window:not([customizing]) toolbar[iconsize="small"] #btn_sessionmanager[disabled="true"], .sessionmanager-icon[disabled="true"] > :is(.menu-iconic-left, .menu-icon) { list-style-image: url("icons/session-manager-16x16-disabled.png"); }

/* for hashcoulouredtabs */
#content[hashedtabs] .tabbrowser-tab:not([busy]) .tab-icon-image:not([src]) {
  list-style-image: none !important;
}

/*
 *****************************************************************************
 *                                non default theme
*/

/* Chromifox Extreme */
#tabbrowser-tabs[tabmix_skin="cfxec"] #tabbrowser-arrowscrollbox .tabbrowser-tab .tab-close-button {
  display: none !important;
}

/* Vfox3 */
#tabbrowser-tabs[tabmix_skin="Vfox3"][tabmix-multibar] #tabbrowser-arrowscrollbox .tabbrowser-tab {
  height: 24px;
}

/* Phoenity Aura -  Multi-row tabbar for Phoenity Aura theme */
#tabbrowser-tabs[tabmix_skin="CrystalFox"][tabmix-multibar] #tabbrowser-arrowscrollbox .tabbrowser-tab,
#tabbrowser-tabs[tabmix_skin="phoenityaura"][tabmix-multibar] #tabbrowser-arrowscrollbox .tabbrowser-tab {
  height: 22px;
  max-height: 22px;
  margin: 1px 0;
}

#tabbrowser-tabs[tabmix_skin="CrystalFox"][tabmix-multibar] #tabbrowser-arrowscrollbox .tabbrowser-tab:not([selected="true"]):hover,
#tabbrowser-tabs[tabmix_skin="phoenityaura"][tabmix-multibar] #tabbrowser-arrowscrollbox .tabbrowser-tab:not([selected="true"]):hover {
  height: 23px;
  max-height: 23px;
  margin: 0 0 1px;
}

#tabbrowser-tabs[tabmix_skin="CrystalFox"][tabmix-multibar] #tabbrowser-arrowscrollbox .tabbrowser-tab[selected="true"],
#tabbrowser-tabs[tabmix_skin="phoenityaura"][tabmix-multibar] #tabbrowser-arrowscrollbox .tabbrowser-tab[selected="true"] {
  height: 24px;
  max-height: 24px;
  margin: 0;
}

/* CrystalFox_Qute-BigRedBrent */
#tabbrowser-tabs[tabmix_skin="CrystalFox"]:not([tabmix-multibar])[inline="true"] #tabbrowser-arrowscrollbox > toolbarbutton,
#tabbrowser-tabs[tabmix_skin="CrystalFox"][tabmix-multibar] #tabbrowser-arrowscrollbox > toolbarbutton {
  vertical-align: top;
}

/* BlackFox_V1-Blue */
#tabbrowser-tabs[tabmix_skin="BlackFox"]:not([tabmix-multibar])[inline="true"] #tabbrowser-arrowscrollbox > toolbarbutton,
#tabbrowser-tabs[tabmix_skin="BlackFox"][tabmix-multibar] #tabbrowser-arrowscrollbox > toolbarbutton {
  vertical-align: top;
}

/* Vista-aero */
#myTabBarRightBox[vista_aero] {
  align-items: flex-start;
}
