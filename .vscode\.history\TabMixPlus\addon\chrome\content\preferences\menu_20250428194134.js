/* exported gMenuPane */
"use strict";

/** @type {MenuPane} */
var gMenuPane = {
  init() {
    MozXULElement.insertFTLIfNeeded("browser/menubar.ftl");
    MozXULElement.insertFTLIfNeeded("browser/tabContextMenu.ftl");
    MozXULElement.insertFTLIfNeeded("browser/preferences/preferences.ftl");
    Tabmix.setFTLDataId("paneMenu");

    $("pinTab").label = gPrefWindow.pinTabLabel;
    $("togglePinTab").setAttribute("label", gPrefWindow.pinTabLabel);
    $("clearClosedTabs").setAttribute("label", TabmixSvc.getString("undoclosetab.clear.label"));

    const [showBmkTab, showBmkTabs] = [$("showBmkTab"), $("showBmkTabs")];
    document.l10n?.translateElements([showBmkTab, showBmkTabs]).then(() => {
      showBmkTab.label = showBmkTab.label.replace("…", "");
      showBmkTabs.label = showBmkTabs.label.replace("…", "");
    });

    MozXULElement.insertFTLIfNeeded("browser/tabbrowser.ftl");
    const [muted, unmuted] = [$("muteTab"), $("unmuteTab")];
    document.l10n?.setAttributes(muted, "tabbrowser-context-mute-tab");
    document.l10n?.setAttributes(unmuted, "tabbrowser-context-unmute-tab");
    document.l10n?.translateElements([muted, unmuted, $("showBmkTab")]).then(() => {
      muted.label += "/" + unmuted.label;
    });

    if (!Tabmix.isVersion(1270)) {
      gPrefWindow.removeItemAndPrefById("pref_closeDuplicateTabs");
    }

    this.setInverseLinkLabel();

    // we can not modify build-in key with reserved attribute
    // bug 1296863 Stop disabling the "New Tab" command in popups
    // bug 1297342 "reserved" attribute should be on <key> elements
    Object.entries(Shortcuts.keys).forEach(([key, keyData]) => {
      if (keyData.reserved) {
        $(key).hidden = true;
      }
    });

    if (!Shortcuts.keys.browserReload.id) {
      $("browserReload").hidden = true;
    }

    this.initializeShortcuts();
    this.setSlideShowLabel();
    let paneMenu = $("paneMenu");
    if (paneMenu.hasAttribute("editSlideShowKey")) {
      paneMenu.removeAttribute("editSlideShowKey");
      setTimeout(() => this.editSlideShowKey(), 0);
    }

    this.generateTabContextMenuItems();

    gPrefWindow.initPane("paneMenu");
  },

  initializeShortcuts() {
    if (Shortcuts.prefsChangedByTabmix) {
      return;
    }

    let newValue = $Pref("pref_shortcuts").stringValue;
    let shortcuts = $("shortcut-group");
    if (newValue == shortcuts.value) {
      return;
    }

    shortcuts.value = newValue;
    shortcuts.keys = JSON.parse(newValue);

    /** @param {MozShortcutClass} shortcut */
    let callBack = shortcut => {
      const shortcutData = Shortcuts.keys[shortcut.id];
      return shortcutData ? shortcut.valueFromPreferences(shortcutData) : false;
    };
    this.updateShortcuts(shortcuts, callBack);
  },

  _slideShow: "",
  updateShortcuts(aShortcuts, aCallBack) {
    let boxes = Array.from(aShortcuts.childNodes).filter(shortcut => aCallBack(shortcut));
    $("shortcuts-panel").setAttribute("usedKeys", Boolean(boxes.length));
    if (this._slideShow != $("shortcut-group").keys.slideShow) {
      this._slideShow = $("shortcut-group").keys.slideShow;
      this.setSlideShowLabel();
    }
  },

  setSlideShowLabel() {
    let slideShow = $("slideShow");
    let label = slideShow.disabled ? "??" : getFormattedKey(slideShow.key);
    $("slideDelayLabel").value = slideShow.getAttribute("_label")?.replace("#1", label) ?? "";
    gPrefWindow.setDisabled("obs_slideDelay", slideShow.disabled);
  },

  editSlideShowKey() {
    $("menu").selectedIndex = 3;
    let slideShow = $("slideShow");
    let item = $("hide-unused-shortcuts");
    if (!slideShow.hasAttribute("value") && $("shortcuts-panel").getAttribute(item.id) == "true") {
      this.toggleLinkLabel(item);
    }
    slideShow.editBox.focus();
    let shortcuts = $("shortcut-group");
    shortcuts.scrollTop = shortcuts.scrollHeight - shortcuts.clientHeight;
  },

  // for shortcuts panel
  toggleLinkLabel(item) {
    var panel = $("shortcuts-panel");
    var wasShow = panel.getAttribute(item.id) == "false";
    item.value = item.getAttribute(wasShow ? "show" : "hide") ?? "";
    panel.setAttribute(item.id, wasShow);
  },

  // update item showInverseLink label in menu pane
  // when "Links" in Events > Tab Focus changed
  setInverseLinkLabel() {
    var showInverseLink = $("showInverseLink");
    var val = ($Pref("pref_selectTab") || $Pref("pref_selectTab1")).value;
    var label = showInverseLink.getAttribute((val ? "bg" : "fg") + "label") ?? "";
    showInverseLink.setAttribute("label", label);
  },

  get PrivateBrowsingUtils() {
    return Tabmix.lazyGetter(
      this,
      "PrivateBrowsingUtils",
      () =>
        ChromeUtils.importESModule("resource://gre/modules/PrivateBrowsingUtils.sys.mjs")
          .PrivateBrowsingUtils
    );
  },

  /** Dynamically generates checkboxes for tab context menu items */
  generateTabContextMenuItems() {
    // Get browser window and tab context menu
    const browserWindow = Tabmix.getTopWin();
    if (!browserWindow) {
      console.error("Tabmix Error: Could not get browser window");
      return;
    }

    const tabContextMenu = browserWindow.document.getElementById("tabContextMenu");
    if (!tabContextMenu) {
      console.error("Tabmix Error: Could not find tabContextMenu");
      return;
    }

    const {TabmixContext, TabContextMenu} = browserWindow;

    // make sure original order is saved
    if (!TabmixContext._originalOrderSaved) {
      TabContextMenu.updateContextMenu(tabContextMenu);
      TabmixContext._saveOriginalMenuOrder();
      TabmixContext.updateMenuOrder();
    }

    // Get the column containers
    const columns = ["1", "2", "3"].map(i => {
      const column = document.getElementById(`column-${i}`);
      column.innerHTML = "";
      return column;
    });

    // Get all menu items from the tab context menu
    const allItems = Array.from(tabContextMenu.children);
    const allIds = allItems.map(item => item.id);
    const filteredItems = allItems.filter(item => {
      const relatedId = item.id?.replace(/SelectedTabs$|Tabs$/, "Tab");
      // Filter out separators and selected tabs items
      return (
        (relatedId === item.id || !allIds.includes(relatedId)) &&
        item.tagName !== "menuseparator" &&
        item.id !== "context_unpinTab"
      );
    });

    // Calculate items per column
    const itemsPerColumn = Math.max(13, Math.ceil(filteredItems.length / 3));

    // Process each menu item
    filteredItems.forEach((item, index) => {
      let id = item.id;
      if (!id) {
        if (item.getAttribute("class") === "share-tab-url-item") {
          // no id for share-tab-url-item
          id = "shareTabURL";
        } else if (item.getAttribute("data-l10n-id") === "full-screen-autohide") {
          // id was added in Firefox 129 context_fullscreenAutohide
          id = "fullscreenAutohide";
        } else if (item.getAttribute("data-l10n-id") === "full-screen-exit") {
          // id was added in Firefox 129 context_fullscreenExit
          id = "fullscreenExit";
        } else {
          console.log("Tabmix Error: Missing id for tab context menu item", item);
          return;
        }
      }

      // Create checkbox
      const checkbox = document.createXULElement("checkbox");
      checkbox.id = id;

      checkbox.setAttribute("data-pref-source", id.startsWith("tm-") ? "tabmix" : "firefox");
      checkbox.setAttribute("data-pref-key", id.replace(/^context_|^tm-/, ""));

      // Get label from the actual menu item
      let label = item.getAttribute("label") ?? "";
      if (!label) {
        const l10nId = item.getAttribute("data-l10n-id") ?? item.getAttribute("data-lazy-l10n-id");
        if (l10nId) {
          checkbox.setAttribute("data-l10n-id", l10nId);
        } else {
          label = "__MISSING__LABEL__:" + id;
        }
      }
      checkbox.setAttribute("label", label);

      // Determine if this is a Tabmix item
      if (id.startsWith("tm-") || item.hasAttribute("tabmix")) {
        checkbox.setAttribute("data-source", "tabmix");
      }

      columns[Math.floor(index / itemsPerColumn)]?.appendChild(checkbox);
    });

    // fix special lables
    document.l10n?.setAttributes($("context_openANewTab"), "menu-file-new-tab");

    const reloadTabEvery =
      tabContextMenu.querySelector("#tm-autoreloadTab_menu").getAttribute("labelTab") ??
      "Reload Tab Every";
    $("tm-autoreloadTab_menu").setAttribute("label", reloadTabEvery);

    // set mute/unmute tab label
    const muted = $("context_toggleMuteTab");
    const unmuted = document.createXULElement("checkbox");
    muted.parentNode?.insertBefore(unmuted, muted);
    unmuted.hidden = true;
    document.l10n?.setAttributes(muted, "tabbrowser-context-mute-tab");
    document.l10n?.setAttributes(unmuted, "tabbrowser-context-unmute-tab");
    document.l10n?.translateElements([muted, unmuted, $("showBmkTab")]).then(() => {
      muted.label += "/" + unmuted.label;
      unmuted.remove();
    });

    // join pin/unpin tab
    const pinTab = $("context_pinTab");
    const unpinTab = browserWindow.document.getElementById("context_unpinTab");
    pinTab.label += "/" + unpinTab.label;

    // initialize values from preference
    this.tabContextMenuFromPreference(columns[0]?.parentElement);
  },

  /**
   * @typedef {{prefSource: "firefox" | "tabmix"; prefKey: string}} Dataset
   *
   * @typedef {{firefox: string[]; tabmix: string[]}} HiddenItems
   */

  /**
   * Syncs the tab context menu preference to the UI
   *
   * @param {HTMLElement} container
   * @returns {string} The preference value
   */
  tabContextMenuFromPreference(container) {
    console.log("tabContextMenuFromPreference");

    var preference = $Pref(container.getAttribute("preference"));
    const prefValue = preference.stringValue;
    if (prefValue == container.value) {
      return prefValue;
    }

    /** @type {HiddenItems} */
    const hiddenItems = JSON.parse(prefValue);
    container.value = prefValue;

    const checkboxes = container.querySelectorAll("checkbox");
    checkboxes.forEach(checkbox => {
      /** @type {Dataset} */ // @ts-expect-error
      const {prefKey, prefSource} = checkbox.dataset;
      checkbox.checked = !hiddenItems[prefSource]?.includes(prefKey);
    });

    return prefValue;
  },

  /**
   * Syncs the UI state to the tab context menu preference
   *
   * @param {HTMLElement} container
   * @returns {string} The new preference value
   */
  tabContextMenuToPreference(container) {
    const checkboxes = container.querySelectorAll("checkbox");

    /** @type {HiddenItems} */
    const hiddenItems = {firefox: [], tabmix: []};

    checkboxes.forEach(checkbox => {
      if (!checkbox.checked) {
        /** @type {Dataset} */ // @ts-expect-error
        const {prefKey, prefSource} = checkbox.dataset;
        hiddenItems[prefSource].push(prefKey);
      }
    });
    return JSON.stringify(hiddenItems);
  },

  /**
   * Sorts menu items based on the current menu order preference
   *
   * @param {HTMLElement} container - The container with the checkboxes
   */
  sortMenuItems(container) {
    const menuOrder = document.getElementById("tabContextMenu_menuOrder").value;
    // Implementation will depend on how we want to sort the items
    // For now, this is a placeholder
  },
};
