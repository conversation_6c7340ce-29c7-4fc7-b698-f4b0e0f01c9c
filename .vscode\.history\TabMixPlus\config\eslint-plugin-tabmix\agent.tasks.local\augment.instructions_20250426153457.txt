TASK INSTRUCTIONS

Tab mix add menu items to Firefox tab context menu id="tabContextMenu"
we do it by appling an overlay (see: addon/chrome/content/overlay/tabContextMenu.xhtml)
that inject the menu items to the context menu

in TabmixContext.buildTabContextMenu (see: addon/chrome/content/click/click.js) we do some more modifications to the context menu

some user requested that we keep firefox original order

my plane is to add option to toggle the menu items order with 2 options:
 - Tab mix order: 0
 - Firefox order: 1

pref name `extensions.tabmix.tabContextMenu.order` default to 0

first lets make a plan for the change:

- in tabContextMenu.xhtml overlay remove all insertbefore and insertafter
  this will add all our menu items to the end of the context menu

- in TabmixContext.buildTabContextMenu
  - add new function set order to tabmix order
  - add new function set order to firefox order
  - add new general init action, not related to order
    - some label action
    - move to place menu items that are in submenu to the main menu

- in TabmixContext.buildTabContextMenu
  - call general init function
  - based on the pref call the relevant order function

- add new test file for the context menu
  - check the order of the menu items for each setting

- need to decide where and how to save the order of the menu items
  note: we need to support old firefox version that may not include all the menu items


before you suggest any code change, lets make a plan for the change
