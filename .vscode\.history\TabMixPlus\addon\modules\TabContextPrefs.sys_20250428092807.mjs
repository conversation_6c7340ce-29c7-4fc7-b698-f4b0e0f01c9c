import {Services} from "resource://gre/modules/Services.sys.mjs";

/**
 * Module to handle conversion of old boolean tab context menu preferences
 * to a single JSON string preference
 */
export const TabContextPrefs = {
  /**
   * Mapping of old boolean preferences to DOM element IDs
   */
  PREF_MAPPING: [
    { pref: "newTabMenu", id: "context_openANewTab" },
    { pref: "duplicateMenu", id: "context_duplicateTab" },
    { pref: "duplicateinWinMenu", id: "tm-duplicateinWin" },
    { pref: "detachTabMenu", id: "context_openTabInWindow" },
    { pref: "muteTabMenu", id: "context_toggleMuteTab" },
    { pref: "pinTabMenu", id: "context_pinTab" },
    { pref: "moveTabOptions", id: "context_moveTabOptions" },
    { pref: "sendTabToDevice", id: "context_sendTabToDevice" },
    { pref: "shareTabURL", id: "share-tab-url-item" },
    { pref: "showMergeWindow", id: "tm-mergeWindowsTab" },
    { pref: "renameTabMenu", id: "tm-renameTab" },
    { pref: "copyTabUrlMenu", id: "tm-copyTabUrl" },
    { pref: "reopenInContainer", id: "context_reopenInContainer" },
    { pref: "selectAllTabs", id: "context_selectAllTabs" },
    { pref: "reloadTabMenu", id: "context_reloadTab" },
    { pref: "autoReloadMenu", id: "tm-autoreloadTab_menu" },
    { pref: "reloadTabOptions", id: "context_reloadTabOptions" },
    { pref: "undoCloseTabMenu", id: "context_undoCloseTab" },
    { pref: "undoCloseListMenu", id: "tm-undoCloseList" },
    { pref: "closeTabMenu", id: "context_closeTab" },
    { pref: "closeDuplicateTabs", id: "context_closeDuplicateTabs" },
    { pref: "closeTabOptions", id: "context_closeTabOptions" },
    { pref: "docShellMenu", id: "tm-docShell" },
    { pref: "freezeTabMenu", id: "tm-freezeTab" },
    { pref: "protectTabMenu", id: "tm-protectTab" },
    { pref: "lockTabMenu", id: "tm-lockTab" },
    { pref: "bookmarkTabMenu", id: "context_bookmarkTab" },
    { pref: "bookmarkAllTabsMenu", id: "context_bookmarkAllTabs" }
  ],

  /**
   * Convert all old boolean preferences to a single JSON string preference
   * 
   * @returns {string} JSON string containing IDs of menu items that should be hidden
   */
  convertOldPrefsToJson() {
    const tabmixPrefs = Services.prefs.getBranch("extensions.tabmix.");
    const hiddenItems = [];

    // Check each old preference and add its ID to hiddenItems if false
    for (const item of this.PREF_MAPPING) {
      try {
        // Skip if the preference doesn't exist
        if (!tabmixPrefs.prefHasUserValue(item.pref)) {
          continue;
        }

        // If the preference is false, add the ID to hiddenItems
        if (!tabmixPrefs.getBoolPref(item.pref)) {
          hiddenItems.push(item.id);
        }
      } catch (ex) {
        console.error(`Error reading preference ${item.pref}:`, ex);
      }
    }

    // Convert hiddenItems to JSON string
    const jsonString = JSON.stringify(hiddenItems);
    
    // Set the new preference
    tabmixPrefs.setCharPref("tabContextMenu", jsonString);
    
    // Clear old preferences
    for (const item of this.PREF_MAPPING) {
      try {
        if (tabmixPrefs.prefHasUserValue(item.pref)) {
          tabmixPrefs.clearUserPref(item.pref);
        }
      } catch (ex) {
        console.error(`Error clearing preference ${item.pref}:`, ex);
      }
    }

    return jsonString;
  },

  /**
   * Check if old preferences exist and convert them if needed
   * 
   * @returns {boolean} True if conversion was performed
   */
  migrateIfNeeded() {
    const tabmixPrefs = Services.prefs.getBranch("extensions.tabmix.");
    
    // Check if any old preferences exist
    const oldPrefsExist = this.PREF_MAPPING.some(item => {
      try {
        return tabmixPrefs.prefHasUserValue(item.pref);
      } catch (ex) {
        return false;
      }
    });

    // If old preferences exist, convert them
    if (oldPrefsExist) {
      this.convertOldPrefsToJson();
      return true;
    }
    
    return false;
  }
};