TASK INSTRUCTIONS

In addon/chrome/content/preferences/menu.xhtml i have a list
checkboxes of all menu items from "tabContextMenu" lines 115-171

there is separated preference for each menu item to show/hide it.
when checkbox is unchecked and the preference is false i hide the menu
item on TabmixContext.updateTabContextMenu addon/chrome/content/click/click.js

I would like to refactor the code:
1. instead of keep hardcoded list generate the list of checkboxes with javascript
   first time the pref pane is loaded add new function for it to addon/chrome/content/preferences/menu.js.
2. instead of preference for each menu item, use 2 preference, one for Firefox
   build-in menu and one for Tab mix Plus menu items.
   both preference will be a comma separated list of the menu item id's
   that should be HIDDEN, for menu item without id use the class instead as an identifier
3. add small badge after firefox built-in menu items to indicate that they are from firefox
4. sort the menu items according to the preference pref_tabContextMenu_menuOrder
   when user change the preference while the pane is open sort the items accordingly.
5. during development of this feature keep the corrent html in addon/chrome/content/preferences/menu.xhtml
   add new tab and panel after the first tab line 94

before you present any code, show me the plan for this refactor
add if you have any suggestions or improvement
