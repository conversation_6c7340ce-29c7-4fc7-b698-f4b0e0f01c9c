<?xml version="1.0"?>

<!DOCTYPE overlay [
<!ENTITY % tabmixDTD SYSTEM "chrome://tabmixplus/locale/tabmix.dtd" >
%tabmixDTD;
]>

<overlay id="tabmix-tabContextMenu-overlay"
   xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul">

  <popupset id="mainPopupSet">
    <menupopup id="tabContextMenu">
        <menuitem id="context_openANewTab" _insertbefore="context_reloadTab"
                 _afterthis="(&afterthis.label;)"
                 tmp_iconic="menuitem-iconic tabmix-newtab-menu-icon menuitem-with-favicon"/>
        <menuitem id="tm-duplicateinWin" _insertafter="context_duplicateTabs"
                 label="&duplicateinWin.label;" accesskey="&duplicateinWin.accesskey;"/>
<!--
        <menuitem id="context_reloadTab">
        <menuitem id="context_reloadSelectedTabs">
        <menuitem id="context_toggleMuteTab">
        <menuitem id="context_toggleMuteSelectedTabs">
        <menuitem id="context_pinTab">
        <menuitem id="context_unpinTab"/>
        <menuitem id="context_pinSelectedTabs"/>
        <menuitem id="context_unpinSelectedTabs"/>
        <menuitem id="context_duplicateTab"/>
        <menuitem id="context_duplicateTabs"/>
        <menuseparator/>
        <menuitem id="context_selectAllTabs"/>
        <menuitem id="context_bookmarkSelectedTabs"/>
        <menuitem id="context_bookmarkTab"/>
-->

        <menuitem id="tm-mergeWindowsTab" _insertbefore="context_reopenInContainer"
                 key="key_tm_mergeWin" label="&mergeContext.label;" accesskey="&mergeContext.accesskey;"/>
        <menuitem id="tm-renameTab" _insertbefore="context_reopenInContainer"
                 label="&renametab.label;" accesskey="&renametab.accesskey;"/>
        <menuitem id="tm-copyTabUrl" _insertbefore="context_reopenInContainer"
                 label="&copytaburl.label;" accesskey="&copytaburl.accesskey;"/>
<!--
        <menu id="context_reopenInContainer"/>
        <menu id="context_moveTabOptions">
          <menupopup id="moveTabOptionsMenu">
            <menuitem id="context_moveToStart"/>
            <menuitem id="context_moveToEnd"/>
            <menuitem id="context_openTabInWindow"/>
          </menupopup>
        </menu>
        <menu id="context_sendTabToDevice"/>
        <menuseparator>

        TabmixContext.buildTabContextMenu move context_reloadTab to this location
        <menuitem id="context_reloadTab"/>
-->
        <menu id="tm-autoreloadTab_menu" _insertbefore="context_closeTabOptions"
              labelTab="&autoReloadTab.label;" accesskeyTab="&autoReloadTab.accesskey;"
              labelSite="&autoReloadSite.label;" accesskeySite="&autoReloadSite.accesskey;">
           <menupopup data-popup="autoReload"/>
        </menu>
        <menu id="context_reloadTabOptions" _insertbefore="context_closeTabOptions"
            label="Reload Multiple Tabs">
          <menupopup id="reloadTabOptions">
            <!-- add menu item to reload selected tabs -->
            <menuitem id="tm-reloadLeft"
                    label="&reloadleft.label;" accesskey="&reloadleft.accesskey;"/>
            <menuitem id="tm-reloadRight"
                    label="&reloadright.label;" accesskey="&reloadright.accesskey;"/>
            <menuitem id="context_reloadAllTabs"
                    data-lazy-l10n-id="content-blocking-reload-tabs-button"/>
            <menuitem id="tm-reloadOther"
                    label="&reloadother.label;" accesskey="&reloadother.accesskey;"/>
          </menupopup>
        </menu>
        <menuseparator id="tabmix_reloadTabOptions_separator" _insertbefore="context_closeTabOptions" type="tabmix"/>

        <menu id="tm-undoCloseList" _insertbefore="context_closeTabOptions"
                 tmp_iconic="menu-iconic closedtabs-icon"
                 label="&undoCloseListMenu.label;" accesskey="&undoCloseListMenu.accesskey;">
          <menupopup id="tm-undoCloseList-menu"/>
        </menu>
<!--
        <menuitem id="context_closeTab"/>
        <menu id="context_closeTabOptions">
          <menupopup id="closeTabOptions">
            <menuitem id="context_closeTabsToTheStart"/>
            <menuitem id="context_closeTabsToTheEnd"/>
            <menuitem id="context_closeOtherTabs"/>
          </menupopup>
        </menu>
-->
        <menuitem id="tm-closeAllTabs" _insertbefore="context_closeOtherTabs"
                 label="&closeAllTabsMenu.label;" accesskey="&closeall.accesskey;"/>
        <menuitem id="tm-closeSimilar" _insertbefore="context_closeOtherTabs"
                 label="&closeSimilarTab.label;" accesskey="&closeSimilarTab.accesskey;"/>

        <menuseparator id="tabmix_closeTab_separator" _insertbefore="context_undoCloseTab" type="tabmix"/>
        <menu id="tm-docShell" _insertbefore="context_undoCloseTab"
                label="&docShellMenu.label;" accesskey="&docShellMenu.accesskey;">
         <menupopup id="tabmix-docShell-popup">
           <menuitem label="&allowImage.label;" value="Images" class="menuitem-iconic" type="checkbox" checked="true" />
           <menuitem label="&allowFrame.label;" value="Subframes" class="menuitem-iconic" type="checkbox" checked="true" />
           <menuitem label="&allowRedirect.label;" value="MetaRedirects" class="menuitem-iconic" type="checkbox" checked="true" />
           <menuitem label="&allowPlugin.label;" value="Plugins" class="menuitem-iconic" type="checkbox" checked="true" />
           <menuitem label="&allowJavascript.label;" value="Javascript" class="menuitem-iconic" type="checkbox" checked="true" />
         </menupopup>
        </menu>
        <menuitem id="tm-freezeTab" _insertbefore="context_undoCloseTab"
                  label="&freezeTabMenu.label;" accesskey="&freezeTabMenu.accesskey;" type="checkbox"/>
        <menuitem id="tm-protectTab" _insertbefore="context_undoCloseTab"
                  label="&protectTabMenu.label;" accesskey="&protectTabMenu.accesskey;" type="checkbox"/>
        <menuitem id="tm-lockTab" _insertbefore="context_undoCloseTab"
                  label="&lockTabMenu.label;" accesskey="&lockTabMenu.accesskey;" type="checkbox"/>
        <menuseparator id="tabmix_lockTab_separator" _insertbefore="context_undoCloseTab" type="tabmix"/>

<!--
        TabmixContext.buildTabContextMenu
        move context_bookmarkSelectedTabs and context_bookmarkTab to this location
-->
        <menuitem id="context_bookmarkAllTabs"
                  command="Browser:BookmarkAllTabs"
                  data-lazy-l10n-id="menu-bookmarks-all-tabs"/>

    </menupopup>
  </popupset>

</overlay>
