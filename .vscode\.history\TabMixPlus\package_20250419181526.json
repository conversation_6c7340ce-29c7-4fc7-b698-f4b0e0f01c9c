{"name": "tab-mix-plus", "version": "1.0.0", "description": "New Tab Mix Plus for modern Firefox", "type": "module", "scripts": {"lint": "eslint --format stylish --fix --cache --cache-location config/.eslintcache --cache-strategy content", "lint:test": "eslint --format stylish --cache --cache-location config/.eslintcache --cache-strategy content", "lint:clean": "eslint --format stylish", "prepare": "husky config/husky", "stylelint": "stylelint --fix --config config/.stylelintrc.json \"addon/**/*.css\"", "tsc:clean": "node config/typecheck.js", "typecheck": "tsc --build > tsc.local.txt"}, "prettier": "./config/prettier.config.js", "repository": {"type": "git", "url": "git+https://github.com/onemen/TabMixPlus.git"}, "keywords": [], "author": "<EMAIL>", "license": "MPL", "bugs": {"url": "https://github.com/onemen/TabMixPlus/issues"}, "homepage": "https://github.com/onemen/TabMixPlus#readme", "devDependencies": {"@eslint/js": "^9.24.0", "@stylistic/eslint-plugin": "^4.2.0", "@stylistic/eslint-plugin-js": "^4.2.0", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-html": "^8.1.2", "eslint-plugin-mozilla": "^4.2.1", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-tabmix": "file:./config/eslint-plugin-tabmix", "globals": "^16.0.0", "husky": "^9.1.7", "lint-staged": "^15.5.0", "prettier": "^3.5.3", "prettier-plugin-jsdoc": "^1.3.2", "stylelint": "^16.18.0", "stylelint-config-standard": "^38.0.0", "typescript": "^5.8.3", "typescript-eslint": "^8.29.0"}}