{
  "javascript.validate.enable": true,
  "typescript.validate.enable": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": "always",
    "source.fixAll": "always",
    "source.fixAll.stylelint": "always"
  },
  // "[javascript][addon/modules/BrowserDOMWindow.sys.mjs]": {
  //   "editor.codeActionsOnSave": {
  //     "source.organizeImports": "never"
  //   }
  // },
  "prettier.ignorePath": "./config/.prettierignore",
  "prettier.configPath": "./config/prettier.config.js",
  // "eslint.packageManager": "npm",
  "eslint.useFlatConfig": true,
  // TODO: set this for debugging
  // "eslint.debug": true,
  // "eslint.trace.server": "verbose",
  "eslint.options": {
    // "overrideConfigFile": "./config/eslint.config.js",
    "cache": true,
    "cacheLocation": "config/.eslintcache"
    // these configuration was used before eslint 9
    // "extensions": [".js", ".jsm", ".xhtml", ".mjs"],
    // "ignorePath": "config/.eslintignore"
  },
  "files.associations": {
    "*.xhtml": "html",
    "*.svg": "html",
    "._log": "log",
    "*._log": "log",
    "*.js": "javascriptreact",
    "*.jsm": "javascript",
    "*.mjs": "javascript",
    "*.sys.mjs": "javascript",
    "*.local.txt": "log",
    "*.json": "jsonc",
    "package.json": "json",
    "**/*.*.js": "javascript",
    "**/*.*.mjs": "javascript",
    "**/*.*.cjs": "javascript"
  },
  "cSpell.customDictionaries": {
    "tabmix": {
      "name": "tabmix-words",
      "path": "${workspaceRoot}/.vscode/tabmix-words.txt",
      "description": "Words used in tabmixplus",
      "addWords": true
    }
  },
  "cSpell.enabledFileTypes": {
    "log": true,
    "!css": false
  },
  "eslint.lintTask.enable": true,
  "eslint.lintTask.options": "--ignore-path config/.eslintignore --ext .js,.jsm .",
  "eslint.run": "onType",
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "json",
    "jsonc",
    "*.sys.mjs"
  ],
  "eslint.probe": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "xml",
    "jsm",
    "mjs",
    "html",
    "json",
    "jsonc"
  ],
  "[jsonc]": {
    "editor.formatOnPaste": true,
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[yaml]": {
    "editor.formatOnPaste": true,
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascript]": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.formatOnPaste": true,
    "editor.formatOnSave": true,
    // "editor.defaultFormatter": "vscode.typescript-language-features",
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "always"
    }
  },
  "[typescript-definition]": {
    "editor.formatOnSave": true,
    "editor.formatOnPaste": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "typescript.tsdk": "node_modules\\typescript\\lib",
  "html.validate.styles": false,
  "augment.chat.userGuidelines": "This project is a Firefox browser extension written in Javascript.\nIt uses:\n- Firefox's codebase and APIs\n- JSDoc for type checking\n- TypeScript definitions (.d.ts files) for type safety\n- Firefox-specific DOM APIs and XUL/XHTML elements\n- Ignore files in .vscode/.history",

  // try to speed up commit in vscode
  "git.enableCommitSigning": false,
  "git.fetchOnPull": false,
  "git.autofetch": false,
  "git.confirmSync": false,

  // for vscode-stylelint
  "css.validate": false,
  "less.validate": false,
  "scss.validate": false,
  "stylelint.configFile": "./config/.stylelintrc.json"

  // "editor.fontFamily": "'Fira Code', 'Cascadia Code', Consolas, 'Courier New', monospace",
  // "editor.fontLigatures": true,
  // "editor.fontSize": 14,
  // "editor.lineHeight": 1.5,
  // "editor.letterSpacing": 0.5,

  // "editor.tokenColorCustomizations": {
  //   "textMateRules": [
  //     {
  //       "scope": "comment.block.documentation",
  //       "settings": {
  //         "fontStyle": "italic",
  //         "foreground": "#608b4e"
  //       }
  //     }
  //   ]
  // }

  // "gitlens.advanced.messages": {
  //   "suppressCommitHasNoPreviousCommitWarning": true
  // },
  // "gitlens.advanced.caching.enabled": true,
  // "gitlens.codeLens.enabled": false
  // "gitlens.currentLine.enabled": false
  // "gitlens.hovers.enabled": false,
  // "gitlens.statusBar.enabled": false,
  // "gitlens.mode.active": "zen"

  // "editor.links": true,
  // "workbench.editor.enablePreview": true,
  // "problems.decorations.enabled": true,
  // "typescript.referencesCodeLens.enabled": true,
  // "editor.gotoLocation.multipleDefinitions": "goto",
  // "editor.gotoLocation.multipleReferences": "goto"
}
