FINISHED TASKS - don't do any task from this file

Task 1:

I need help refactoring my Firefox browser extension's eslint-plugin-tabmix utility code.

Task: Move the `generateBlameDates` function from `git-blame-dates.js` to a new file called `blame-core.js`.

Steps needed:
1. Create a new file `blame-core.js` that:
   - Exports the `generateBlameDates` function (currently in git-blame-dates.js)
   - Adds an options parameter to support a `cwd` option for the git command
   - Exports the `CURRENT_DATE` constant
   - Includes proper JSDoc comments

2. Update `git-blame-dates.js` to:
   - Import `generateBlameDates` and `CURRENT_DATE` from the new file
   - Remove the local implementation of the function

3. All git-blame-dates.test.js tests are currently passing, and I want to maintain that. Update the only `git-blame-dates.test.js` to use the new `generateBlameDates` function

4. Don't update any other files yet.

5. If you stuck on something, stop and ask for more instructions.

Task 2:

`generateBlameDates` was moved successfully to blame-core.js
all tests are passing

Check if other tests file other then `git-blame-dates.test.js` is testing
`generateBlameDates` that comes from blame-core.js.
we already know that blame-worker-direct.test.js is using its own version

this task is just for checking, report back, but don't change any code.

Task 3:

Update blame-worker.js and blame-worker-direct.test.js and any other files
to use the new `generateBlameDates` function from `blame-core.js`.

Task 4:

1. blame-worker still exports `generateBlameDates` why?
2. does the tests of `generateBlameDates` in blame-worker-direct.test.js are
   using the worker?, if they don't maybe all `generateBlameDates` test can be
   move to blame-core.test.js, including the tests from git-blame-dates.test.js

Task 5:

instead of mocking "node:child_process" in blame-core.test.js,
i think it is better to extract the call to execSync to helper function
and mock/spy on that function.

see how i use runGitCommand from git-utils.js in manage-blame-cache.js
and manage-blame-cache.test.js.

If you have better idea regarding this subject let me know before you do it

Task 6:

I was manged to use spyOn runGitBlameCommand by moving it to git-utils.js
do you thing it is better approach the mocking "node:child_process" ?

do we need `"should run git blame command and return the output"` in blame-core.test.js?
it is just check the return from the mock

regarding git-utils.js, can you check why runGitCommand does not have any coverage?
`export function runGitCommand(command) {
  return execSync(command, {cwd: projectRoot, encoding: "utf8"});
}`

Task 7:

modify `"should run git blame command and return the output"` in blame-core.test.js
to do direct test on runGitCommand (vi.restoreAllMocks();)

- when you run it on test file we now the the output will be current timestamp for all lines
- run also 2nd time with test file without setting cwd, expect it to throw an error

Task 8:

cache-consistency.test.js test before that blame-worker and git-blame-dates have the same output
but now that we have one getCacheFilePath function the test "should generate consistent cache filenames between modules"
is useles. it compare the output of the same function
getCacheFilePath form blame-worker.js was imported from cache-utils.js
so we can remove the test and the getCacheFilePath from blame-worker.js

maybe we should export getCacheFilePath also from git-blame-dates.js and check that both functions are the same

Let me know the steps you are going to take to complete this task before you take any action.

Task 9:

the functions getBlameDatesForFile in blame-worker and
getNormalBlameDates in git-blame-dates are almost identical.
in git-blame-dates another function have the name getBlameDatesForFile - this is confusing

is it possible to:

- rename getBlameDatesForFile in git-blame-dates to better name
- rename getNormalBlameDates in git-blame-dates to getBlameDatesForFile
- try to extract common from blame-worker and git-blame-dates to a
  new function in blame-core.js, maybe better name for it will be
  getBlameDatesFromGit or something like that
- remove tests for duplicate code from git-blame-dates.test.js and blame-worker-direct.test.js
  and test the new function in blame-core.test.js

before you do any action show me the plane

Task 10:

in blame-worker-direct.test.js we have:

// Mock child_process module
vi.mock("node:child_process", () => {
  const mockExecSync = vi.fn((_command, _options) => {
    // Extract the file path from the command
    const match = _command.match(/"([^"]+)"/);
    const filePath = match ? match[1] : "";

    // Return different blame outputs based on the file
    if (filePath.includes("error")) {
      throw new Error("Command failed");
    } else if (filePath.includes("empty")) {
      return "";
    } else if (filePath.includes("invalid")) {
      return "Invalid blame output";
    } else {
      // Return a valid blame output with timestamps
      return [
        "^5d7e9f4 (Author Name 1234567890 2023-01-01 12:00:00 +0000 1) Line 1",
        "^5d7e9f4 (Author Name 1234567891 2023-01-02 12:00:00 +0000 2) Line 2",
        "00000000 (Not Committed Yet 2023-01-03 12:00:00 +0000 3) Line 3",
        "",
      ].join("\n");
    }
  });

  return {
    execSync: mockExecSync,
  };
});

in blame-core.test.js we have:
vi.spyOn(gitUtils, "runGitBlameCommand").mockImplementation((filePath, _options) => {
  // Return different blame outputs based on the command
  if (filePath.includes("error-file.js")) {
    throw new Error("Command failed");
  } else if (filePath.includes("empty-file.js")) {
    return "";
  } else if (filePath.includes("invalid-timestamp.js")) {
    return [
      "^5d7e9f4 (Author Name 1234567890 2023-01-01 12:00:00 +0000 1) Line 1",
      "^5d7e9f4 (Author Name invalid_timestamp 2023-01-02 12:00:00 +0000 2) Line 2",
      "^5d7e9f4 (Author Name 2023-01-03 12:00:00 +0000 3) Line 3", // Missing timestamp
      "",
    ].join("\n");
  } else {
    // Return a valid blame output with timestamps
    return [
      "^5d7e9f4 (Author Name 1234567890 2023-01-01 12:00:00 +0000 1) Line 1",
      "^5d7e9f4 (Author Name 1234567891 2023-01-02 12:00:00 +0000 2) Line 2",
      "00000000 (Not Committed Yet 2023-01-03 12:00:00 +0000 3) Line 3",
      "",
    ].join("\n");
  }
});

since we always use runGitBlameCommand you can use same spyOn for both test files

Task 11:

in git-blame-dates getBlameDatesForFile was renamed to getBlameDatesWithSourceText
the test was fixed, however the are other code that still use the old name:
 utils/filter-processor.js
 tests/filter-processor.test.js
 tests/mocks/git-blame-dates.js

Task 12:

there is probably a bug in main and we dont have a test that find this error

step to reproduce:
 - run `npm run rebuild-blame-cache --prefix config/eslint-plugin-tabmix`
   this command will rebuild all cache
 - make changes to some file and commit the changes

result
 - all files removed from the cache except for 4 files
    22450232-prettier_config.csv
    22450232-typecheck.csv
    5ed7c8e7-bootstrap.csv
    eslint_config.csv
 - that files that where committed are not in the cache

i set the current branch to `wip/fix-cache-bug`
you can add test files with content to test how it trigger the post-hook
make sure that the file name is valid name not in the ignore list for cache
see `config/eslint-plugin-tabmix/utils/eslint-ignores.js`

if you can not find the bug, prepare a list of places for me to check

good luck

Task 13:

Firefox now can have in the tabs toolbar tabs and tab group

group markup is as follow:
`<vbox class="tab-group-label-container" pack="center">
  <label class="tab-group-label" role="button"/>
</vbox>
<html:slot/>
`

tab group element have `display: contents;` so it does not tack any space
tab group have labelElement group.labelElement
the label element is in container -> group.labelElement.parentElement

in the function `adjustNewtabButtonVisibility` (its in addon/chrome/content/tab/tab.js line 763)
i calculate the position of the new tab button after last tab.
the goal is to have the button in the same row as the last tab,
if this is not possible the button should be on the right side of the tabs toolbar.

currently the function use Tabmix.visibleTabs to get lastTab and previousTab.
It also considering some cases of group at the end of the tab strip see lines 821-822 in tab.js

i have a getter to get  `allVisibleItems` in addon/chrome/content/minit/minit.js
(maybe i need to move it to Tabmix.tabsUtils? or Tabmix.visibleTabs)

Refactor goals:
- The function need to be updated to use allVisibleItems instead of visibleTabs
- use lastElement and previousElement instead of lastTab and previousTab
- handle the case lastElement or previousElement are collapsed group, in collapsed group
  only group.labelElement.parentElement is visible.
  maybe useing lastElement and previousElement will handle this case automatically
- recommend a place for allVisibleItems getter

Task 14:

Firefox now can have in the tabs toolbar tabs and tab group
it have these utility functions:
  gBrowser.isTabGroupLabel(element)
  gBrowser.isTabGroup(element)
  gBrowser.isTab(element)

In places.js (C:/code/TabMixPlus/addon/chrome/content/places/places.js)
I have openGroup function to open bookmark set, it is called from PlacesUIUtils.openTabset
I need to keep the the name openGroup for compatibility with TreeStyleTab

Help me refactor the openGroup function to support tab groups
 - don't reuse blank tabs from group
 - don't open bookmarks in group when selected tab is in a group
 - don't replace the group even if the pref is on
 - open the bookmarks after the group or at the end according to the pref

Task 15:

Tab mix add menu items to Firefox tab context menu id="tabContextMenu"
we do it by appling an overlay (see: addon/chrome/content/overlay/tabContextMenu.xhtml)
that inject the menu items to the context menu

in TabmixContext.buildTabContextMenu (see: addon/chrome/content/click/click.js) we do some more modifications to the context menu

some user requested that we keep firefox original order

my plane is to add option to toggle the menu items order with 2 options:
 - Tab mix order: 0
 - Firefox order: 1

pref name `extensions.tabmix.tabContextMenu.order` default to 0

first lets make a plan for the change:

- in tabContextMenu.xhtml overlay remove all insertbefore and insertafter
  this will add all our menu items to the end of the context menu

- in TabmixContext.buildTabContextMenu
  - add new function set order to tabmix order
  - add new function set order to firefox order
  - add new general init action, not related to order
    - some label action
    - move to place menu items that are in submenu to the main menu

- in TabmixContext.buildTabContextMenu
  - call general init function
  - based on the pref call the relevant order function

- add new test file for the context menu
  - check the order of the menu items for each setting

- need to decide where and how to save the order of the menu items
  note: we need to support old firefox version that may not include all the menu items


before you suggest any code change, lets make a plan for the change
