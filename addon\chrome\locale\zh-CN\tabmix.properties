extensions.{dc572301-7619-498c-a57d-39143191b318}.description=加强标签页浏览功能。
tmp.merge.warning.title=关闭窗口且不合并标签页
tmp.merge.warning.message=该选择的标签页将和另一个窗口合并,同时关闭其它标签页和当前窗口。
tmp.merge.warning.checkbox=关闭不会合并标签页的窗口时，始终警告您。
tmp.merge.error=至少要有两个已打开的窗口才能合并
tmp.merge.private=您无法合并隐私窗口和非隐私窗口。
tmp.importPref.error1=该文件无效，无法导入。
tmp.importPref.error2=导入设置失败。
tmp.sessionempty=下一次您启动浏览器时,\'上一次的浏览进程\'将清空。
droptoclose.label=拖放并关闭标签页
flstOn.label=激活上一次选中的标签页
flstOff.label=激活右边的标签页
slideshowOn.label=标签页滚动已打开
slideshowOff.label=标签页滚动已关闭
undoclosetab.keepOpen.label=保持菜单打开
undoclosetab.keepOpen.description=点击切换
undoclosetab.clear.label=清空已关闭的标签页列表
undoclosetab.clear.accesskey=C
undoClosedWindows.clear.label=清空已关闭的窗口列表
undoClosedWindows.clear.accesskey=C
protectedtabs.closeWarning.1=您已选择关闭 %S 受保护标签页。您是否确定继续？
protectedtabs.closeWarning.2=您已选择关闭 %S 受保护标签页。您是否确定继续？
protectedtabs.closeWarning.3=您已选择关闭 %S 标签页，其中 %S 受保护。您是否确定继续？
protectedtabs.closeWarning.5=关闭有已保护标签页的窗口时警告您
window.closeWarning.2=关闭有多个标签页的窗口时警告您
closeWindow.label=关闭窗口
confirm_autoreloadPostData_title=警告！
confirm_autoreloadPostData=您启用自动重新载入的页面包含 POSTDATA。n如果您启用自动重新载入，表单执行的任何操作（例如在线购买）将会被重复。nn您是否确定要启用自动重新载入？
confirm_autoreloadPostData_remote=你尝试启用自动载入功能的页面包含 POSTDATA。\n如果你启用自动载入，任何表单要执行的操作（例如在线支付）都会丢失。\n\n你确定要启用自动载入功能吗？
incompatible.title=Tab Mix Plus
incompatible.msg0=下列扩展已被整合或与 Tab Mix Plus 不兼容。
incompatible.msg1=您是否要禁用这些扩展？
incompatible.msg2=不兼容的扩展
incompatible.button0.label=禁用
incompatible.button0.accesskey=D
incompatible.button1.label=不禁用
incompatible.button1.accesskey=O
incompatible.button2.label=禁用并重新启动
incompatible.button2.accesskey=E
incompatible.chkbox.label=浏览器启动时显示该提示
tabmixoption.error.title=Tabmix 错误
tabmixoption.error.msg=您必须为浏览窗口启用 TabMix 选项
# LOCALIZATION NOTE (rowsTooltip.rowscount):
# Semicolon-separated list of plural forms. See:
# http://developer.mozilla.org/en/docs/Localization_and_Plurals
# #1 is the total number of rows
# The singular form is not considered since this string is used only for
# multiple rows.
rowsTooltip.rowscount=第一行
rowsTooltip.activetab=选定位于第1行的标签页
bugReport.label=报告bug
