/**
    Firefox version 119+

    Linux platform

**/

/***  Tab close button when user set background color ***/
#TabsToolbar[brighttext] #tabbrowser-tabs[tabmix_otherStyle~="bg"] #tabbrowser-arrowscrollbox .tabbrowser-tab:not([visuallyselected], [tabmix_tabState], :hover) > .tab-close-button,
#TabsToolbar[brighttext] #tabbrowser-tabs[tabmix_unloadedStyle~="bg"] #tabbrowser-arrowscrollbox .tabbrowser-tab:not([visuallyselected])[tabmix_tabState="unloaded"]:not(:hover) .tab-close-button,
#TabsToolbar[brighttext] #tabbrowser-tabs[tabmix_unreadStyle~="bg"] #tabbrowser-arrowscrollbox .tabbrowser-tab:not([visuallyselected])[tabmix_tabState="unread"]:not(:hover) .tab-close-button {
  /* noinspection CssInvalidFunction */
  background-image: -moz-image-rect(url("chrome://global/skin/icons/close.svg"), 0, 16, 16, 0);
}
