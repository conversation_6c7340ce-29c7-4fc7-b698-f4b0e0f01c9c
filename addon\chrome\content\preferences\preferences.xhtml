<?xml version="1.0"?>

<?xml-stylesheet href="chrome://global/skin/global.css"?>
<?xml-stylesheet href="chrome://tabmixplus/skin/preferences.css"?>
<?xml-stylesheet href="chrome://tabmixplus/skin/shortcuts.css"?>

<!DOCTYPE prefwindow [
<!ENTITY % pref-tabmixDTD SYSTEM "chrome://tabmixplus/locale/pref-tabmix.dtd">
%pref-tabmixDTD;
<!ENTITY % tabmixDTD SYSTEM "chrome://tabmixplus/locale/tabmix.dtd">
%tabmixDTD;
]>

<prefwindow type="prefwindow"
            class="prefwindow system-font-size"
            id="TabMIxPreferences"
            windowtype="mozilla:tabmixopt"
            title="&page.header.title;"
            xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"
            autosize="true"
            onload="setDialog();"
            ondialogaccept="gPrefWindow.deinit();"
            ondialogcancel="gPrefWindow.deinit();"
            ondialogextra1="gPrefWindow.onApply();"
            ondialoghelp="openHelp();"
            buttons="accept,cancel,extra1,extra2"
            buttonlabelextra1="&apply.label;"
            buttonlabelextra2="&settings.label;"
            minwidth="527"
            persist="screenX screenY"
            tabindex="0">

  <!-- scripts -->
  <script type="application/javascript" src="chrome://tabmixplus/content/utils.js"/>
  <script type="application/javascript" src="chrome://tabmixplus/content/preferences/prefs-ce.js"/>
  <script type="application/javascript" src="chrome://tabmixplus/content/preferences/checkbox_tmp-ce.js"/>
  <script type="application/javascript" src="chrome://tabmixplus/content/preferences/shortcuts-ce.js"/>
  <script type="application/javascript" src="chrome://tabmixplus/content/broadcaster.js"/>
  <script type="application/javascript" src="chrome://tabmixplus/content/preferences/numberinput.js"/>
  <script type="application/javascript" src="chrome://tabmixplus/content/preferences/preferences.js"/>

  <prefpane id="paneLinks" label="&tab.links;" class="prefpane"
            src="chrome://tabmixplus/content/preferences/links.xhtml"/>
  <prefpane id="paneEvents" label="&tab.events;" class="prefpane"
            src="chrome://tabmixplus/content/preferences/events.xhtml"/>
  <prefpane id="paneAppearance" label="&tab.appearance;" class="prefpane"
            src="chrome://tabmixplus/content/preferences/appearance.xhtml"/>
  <prefpane id="paneMouse" label="&tab.mouse;" class="prefpane"
            src="chrome://tabmixplus/content/preferences/mouse.xhtml"/>
  <prefpane id="paneMenu" label="&tab.menu;" class="prefpane"
            src="chrome://tabmixplus/content/preferences/menu.xhtml"/>
  <prefpane id="paneSession" label="&tab.session;" class="prefpane"
            src="chrome://tabmixplus/content/preferences/session.xhtml"/>
  <prefpane id="paneIncompatible" label="Error" class="prefpane"
            src="chrome://tabmixplus/content/preferences/incompatible.xhtml"/>
  <prefpane id="broadcasters" hidden="true" class="prefpane">
    <preferences>
      <preference id="pref_singleWindow" name="extensions.tabmix.singleWindow" type="bool"
                  onchange="if (typeof gLinksPane == 'object') gLinksPane.singleWindow(this.value);"/>
      <preference id="pref_undoClose"    name="extensions.tabmix.undoClose"    type="bool"/>
      <preference id="pref_instantApply" name="extensions.tabmix.instantApply" type="bool"
                  onchange="toggleInstantApply(this);"/>
    </preferences>
    <broadcasterset id="main:Broadcaster">
      <broadcaster id="obs_singleWindow" inverseDependency="true"/>
      <broadcaster id="obs_undoClose"/>
    </broadcasterset>
  </prefpane>

  <popupset>
    <menupopup id="tm-settings" position="before_start">
      <menuitem id="tm-defaults" label="&settings.default;" oncommand="defaultSetting();"/>
      <menuitem id="tm-import" label="&settings.import;" oncommand="importData();"/>
      <menuitem id="tm-export" label="&settings.export;" oncommand="exportData();"/>
      <menuitem id="syncPrefs" label="&settings.sync;" oncommand="toggleSyncPreference(); this.checked=!this.checked;" type="checkbox"/>
      <menuitem id="instantApply" label="Instant Apply" oncommand="toggleInstantApply(this);" type="checkbox"/>
    </menupopup>
  </popupset>

</prefwindow>
