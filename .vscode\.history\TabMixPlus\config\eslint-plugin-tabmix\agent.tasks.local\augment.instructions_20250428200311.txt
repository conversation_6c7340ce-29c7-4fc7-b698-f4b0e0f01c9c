TASK INSTRUCTIONS


lets use the new `extensions.tabmix.tabContextMenu` in addon/chrome/content/preferences/menu.xhtml

since it is a custom preference we need onsyncfrompreference and onsynctopreference
or valueFromPreferences setter/getter see in addon/chrome/content/preferences/menu.js
how i did it for addon/chrome/content/preferences/menu.js

add the pref to "tab-context-menu-container"




now i need to modify updateTabContextMenu to use the new new preference
"extensions.tabmix.tabContextMenu"

1. parse the tabContextMenu preference
2. add new internal function to return true if the item is NOT hidden
3. replace each call to Tabmix.prefs.getBoolPref with the value from the new function
   the argument for the new function is the id of the menu item without its prefix
   its need to match the value in TAB_CONTEXT_MAPPING (addon/modules/PreferenceMigrator.sys.mjs)
