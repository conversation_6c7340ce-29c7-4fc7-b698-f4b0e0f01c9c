--------------------------------------------------------------------------------------------------
2025-04-18
 - fix types not to use
   type F = Bar & Baz & {....}
   much faster to use
   interface F extends Bar, Baz {...}
   read https://github.com/microsoft/TypeScript/wiki/Performance
 - "@typescript-eslint/no-unsafe-declaration-merging": "error", - NO ERRORS

 investigate eslint.config.js, to find what slow it down
  - its takes 2.2s just to evaluate the config
  - split linting .d.ts file ?
  - remove prettier from eslint and run it separately
  - update vscode config
--------------------------------------------------------------------------------------------------
2025-04-18
  - https://onemen.github.io/tabmixplus-docs/releases/dev-build/
    Last updated - is not the right date
--------------------------------------------------------------------------------------------------
2025-04-17
 when dragging last tab to the right drop indicator should not be visible after it
 unless it is in a group, then dragging after itself remove it from the group
--------------------------------------------------------------------------------------------------
2025-04-16
 - need to find a way to make sure all blame updates are done with workers before eslint start
 - try to use async exec instead of execSync - maybe i can use async for the worker
 - add integration test that use eslint test the output
 - add integration test that use eslint and test what happen when there is an error in our plugin
 - try to use les vi.mock, move import to the top
 - add function that wrap execSync instead of mocking child_process
   in mocks/git-blame-dates.js and blame-worker-direct.test.js
 - do we have to export getCacheFilePath from blame-worker
 - we have generateBlameDates and getBlameDatesForFile in both blame-worker and git-blam-dates
 - check if i need mocks/git-blame-dates.js
 - check all mocks
 - clean other tests
 - add test for blame-core, se if there are duplicate tests that can be simplify
   if we test blame-core, maybe we can mock it when we test git-blame-dates and blame-worker
 - fix the ignure to allow to cache fixures files, so we can predict the timestamp
   use fixtures instead of other real files
 - need same check `const hasInvalidTimestamps = timestamps.some(t => isNaN(t) || !isFinite(t));`
   when we create the timestamp not just when we read the file
   add test to assert the content of the file
 - instead of vscode-detection.js we need to find a way to work with any IDE that use eslint extension
 - check every it(...) test remove duplicate
 - check performance, try to update blame from pre-processor (regardless if the file have messages)
   check if i need to wait in the post-processor to async operation to finished
   check if eslint only call processor for files not in its cache
--------------------------------------------------------------------------------------------------
2025-04-14
 - there are two getCacheFilePath in blame-worker.js and git-blame-dates.js
 - check if i can impoert `const childProcess = await import("node:child_process");`
   and just update the spy for each test
 - how to mock import file with `process.argv.includes("--rebuild")`
 - load manage-blame-cache.js once at the top
   `const manageBlameCacheModule = await import("../utils/manage-blame-cache.js");`
   then do 2 tests, one with --rebuild and one without
--------------------------------------------------------------------------------------------------
2025-04-12
  - check how to use Firefox types MozTabbrowserTab, MozTabbrowserTabGroup
--------------------------------------------------------------------------------------------------
2025-04-10
 V use number instead of date from blame - check if this is better, if we get date from cache
 - check if it is faster to use build in node to read JSON files
 - pass cutoofDate from ealint.config setting, check how to use context.settings on postprocess
 V add helper go get root dir - some utils and test does not work after files moved to sub direcories
 - rename test files to filename.test.js
 - refactore to use Eslint test runner or other test siute
 - check cache - it does not includes all the files
--------------------------------------------------------------------------------------------------
2025-04-06
 - when opening bookmarks - dont reuse blank tabs from group, and dont open bookmarks in group
   open the bookmarks after the group
--------------------------------------------------------------------------------------------------
2025-04-09
 - check if merging lazy into snadbox triggers the load of the script
--------------------------------------------------------------------------------------------------
2025-04-09
 - update release menu on the docs to show sub menu of old releas, many by year
 - add release date after the release version
 - maybe add firefox version each release is worked on?
--------------------------------------------------------------------------------------------------
2025-04-07
 - start adding test with AI - use test tool to run in browser test
 - check why commiting with vscode become very slow (maybe some extension is the cause, or husky?)
--------------------------------------------------------------------------------------------------
2025-04-07
 - Drag & Drop Changed Since Refactor for Tab Groups #427
 - right scroll arrow never disaled when last ta is visibleb
  there is an issue in waterfox, check also 128 drop marker is visible on the right when
  dropping a link in single line when it is overflow

21:46:26.013 TypeError: aWindow is null
    getContentSize resource://gre/modules/PageThumbUtils.sys.mjs:111
    createSnapshotThumbnail resource://gre/modules/PageThumbUtils.sys.mjs:211
    _captureToCanvas resource://gre/modules/PageThumbs.sys.mjs:303
    captureToCanvas resource://gre/modules/PageThumbs.sys.mjs:237
    startTabDrag chrome://browser/content/tabbrowser/tabs.js:542
    on_dragstart chrome://browser/content/tabbrowser/tabs.js:447
    on_dragstart chrome://tabmixplus/content/minit/minit.js:657
    handleEvent chrome://browser/content/tabbrowser/tabs.js:1858
tabs.js:544:31
--------------------------------------------------------------------------------------------------
2025-04-06
 V fix sandbox errors after `Cu.nukeSandbox(sandbox);`
 V when using snadbox for options dialog for zen, need to clear it when the dialog is closed
 V try to reduce the scope of SystemGlobal sandbox, use only the parent object and extand if needed
 X consider adding separate sandbox for tabcontainer and searchbar
 - examin the possibility to run `Cu.evalInSandbox` on multiple function to a single filename
 - drop support versions before 128 for next version ?
--------------------------------------------------------------------------------------------------
2025-04-01
 - check if i can use tab.isEmpty instead of isBlankTab, isBlankBrowser
--------------------------------------------------------------------------------------------------
2025-03-30
  - check i it is possible to load dynamic rules early for style tab colors
  - speeded up setting style preferences on tabs
  - check if i can simplify dynamic tab styles with the example from
    web-dev-simplified youtube video https://www.youtube.com/watch?v=VA975GOUFmM&t=387s
--------------------------------------------------------------------------------------------------
2025-03-25
  check why sometimes tabbar enter to overflow in multi-row when it does not need to
 --------------------------------------------------------------------------------------------------
2025-03-19
  FIXME
  V title from bookmark don't apply when dragging tab to new window

  Group Issues:
  ? scroll buttons not always scroll to full row, maybe only when last the in the top row is group
    try to adjust it in click event
    I can not reproduce it again
  V new button added before the group when selected tabs is in the group
  V consider adding option to where to open new tab when current tab is in a group
    or maybe open next to current when it is not in a group, if it is open in the end
  - need to add css style to keep tab-label and its tabs together or at least with the first tab
  V add line bellow dragged tab when it is dragged over group
--------------------------------------------------------------------------------------------------
2025-03-12
test typescript-go
C:/code/typescript-go/built/local/tsgo -project C:/code/TabMixPlus/addon/modules > C:/code/TabMixPlus/tsc.local.txt

 - running tsconfig.json from the root did not do anything
 - currently it does not support working on .js or .mjs files
--------------------------------------------------------------------------------------------------
2025-03-10
 when opening bookmarks and the pref to set title from bookmark is off, need to set title from url
 for pending tabs / or always use title from bookmarks fro pending tabs
--------------------------------------------------------------------------------------------------
2025-03-05
 - epic shop - add possibility to stop running processes from admin page
--------------------------------------------------------------------------------------------------
2025-03-03
  - add new repository for Firefox scripts https://github.com/xiaoxiaoflood/firefox-scripts
--------------------------------------------------------------------------------------------------
2025-02-27
 - add ui for vertical tabs
 - move scroll options to mouse section
--------------------------------------------------------------------------------------------------
2025-02-27
  - test wrap shell https://www.warp.dev/
  - test biome https://biomejs.dev/
--------------------------------------------------------------------------------------------------
2025-02-12
 start working on changeing `eval` to `Services.scriptloader.loadSubScript`
 try to combined all code from an object/scope into one `file` string

 - check possibility to use async ChromeUtils.compileScript with hasReturnValue: true
   check the time to load all scripts
-------------------------------------------------------------------------------------------------
2025-02-09
 tabbar.click_dragwindow - true and tabbar.dblclick_changesize - false,
 first click on tabbar with modifies not got true when the windows was not focused
--------------------------------------------------------------------------------------------------
2025-01-29
João <<EMAIL>>
1:59 AM (20 hours ago)
to tabmix.onemenHello, Onemen!I would like to make a suggestion for Tab Mix Plus, but I prefer to do it in a private environment, because I don't know if it's feasible.It's about pinned tabs. If I have, for example, two pinned tabs and others that are not pinned, when I close the last unpinned tab, Firefox will automatically select a pinned tab, usually the one on the right. Unless I have configured it to select the last previously selected tab when closing the selected tab.I miss a feature that would allow me to completely isolate pinned tabs. In this case, when closing the last unpinned tab, the browser would not automatically select any pinned tab, but would immediately open a new unpinned tab, which would be selected.This would allow for the complete isolation of pinned tabs, which would only be selected manually. I think this should be Firefox's ideal default behavior.Of course, this would be optional.Please review this suggestion and see if it's feasible. And please excuse my English.I've already talked to you on GitHub (my nickname: srjjgm).Thank you
--------------------------------------------------------------------------------------------------
2025-01-24
 check all bugs in: Bug 1776174 - [meta] Cleanup after in-tree and out-of-tree ESM-ification migration
--------------------------------------------------------------------------------------------------
2025-01-16
fix warnings `Please use ChromeUtils.defineLazyGetter instead of TabmixChromeUtils.defineLazyGetter.`
--------------------------------------------------------------------------------------------------
2025-01-15
 - closing all tabs does not save closed group info - it appear in tabs panel
 - save and close group does not appears in closed tabs list
 - reuse blank tab when opening closed group
--------------------------------------------------------------------------------------------------
2025-01-12
 - check astro View Transitions update  https://github.com/withastro/starlight/pull/694
--------------------------------------------------------------------------------------------------
2025-02-25 (last update) - closed tab group related
  2025-02-05
  on Windows 10 - empty group did not closed after removing last closed tab in it

  2025-02-03
  V implement `add to group` possibility when dragging with indicator

  2025-01-12
  - check how saved closed group work
  - check latest update to SessionStore groups
  ```
  <menupopup id="open-tab-group-context-menu">
    <menuitem id="open-tab-group-context-menu_moveToNewWindow" data-l10n-id="tab-group-context-move-to-new-window"/>
    <menuitem id="open-tab-group-context-menu_moveToThisWindow" data-l10n-id="tab-group-context-move-to-this-window"/>
    <menuitem id="open-tab-group-context-menu_delete" data-l10n-id="tab-group-context-delete"/>
  </menupopup>

  <menupopup id="saved-tab-group-context-menu">
    <menuitem id="saved-tab-group-context-menu_openInThisWindow" data-l10n-id="tab-group-context-open-saved-group-in-this-window"/>
    <menuitem id="saved-tab-group-context-menu_openInNewWindow" data-l10n-id="tab-group-context-open-saved-group-in-new-window"/>
    <menuitem id="saved-tab-group-context-menu_delete" data-l10n-id="tab-group-context-delete"/>
  </menupopup>
  ```

  2024-12-05
  - TODO: need to find where do saved and close groups go?????
  - test the different between save closed group and deleted group

  2024-11-26
  - tabgroup indicator may trigger multi-row flicker
    maybe i need to move `new tab` button to the right when there are group in multi-row

  2024-11-23
  V add ability to add dragged tab to group in multi-row mode
  V test update for bug 1932425 on version 133 english and on 133,134 on hebrow also test it with vertical tabs
--------------------------------------------------------------------------------------------------
2025-01-06
 check changes in gBrowser.tabContainer._notifyBackgroundTab
--------------------------------------------------------------------------------------------------
2025-01-05
 - drop link to pinned tabs area should make the link pinned
--------------------------------------------------------------------------------------------------
2024-12-20
 check changes to gBrowser.getWindowTitleForBrowser
--------------------------------------------------------------------------------------------------
2024-12-20
 - title in undo closed tabs not match the url and icon, look like it is off by one
 - check latest changes in RecentlyClosedTabsAndWindowsMenuUtils.sys.mjs
 - check if my code is still blocking RecentlyClosedTabsAndWindowsMenuUtils listeners?
--------------------------------------------------------------------------------------------------
2024-12-14
  - disable bluehost payments
--------------------------------------------------------------------------------------------------
2024-12-09
- add option to hide from Firefox 135
`#firefox-view-button {
  display: none !important;
}`

--------------------------------------------------------------------------------------------------
2024-12-05
 - prevent multi-row scroll when user cancel close tabs action (close all, to right, to left, etc...)
 maybe i can save scroll position and scroll back if user cancel the close action
--------------------------------------------------------------------------------------------------
2024-12-03
 on developer edition 134, opening bookmarks show `new tab` title for pendings tabs
 when `Use bookmark name as tab title is off`
 need to use the url as tab title for pending tabs ?
 maybe use bookmark name as title for pending tabs?
 check if i can get title from places ui

- check SessionStore SessionStore.getLazyTabValue(aTab, "title")
--------------------------------------------------------------------------------------------------
2024-12-01
 - keep user custom name when moving tab to another window
--------------------------------------------------------------------------------------------------
2024-11-23
 - error when opening group of bookmarks when there are blank pendings tabs
   I can reproduce this


 error when closing all tabs (some was pendings): Given tab is not restoring
 18:24:05.866 Given tab is not restoring. SessionStore.sys.mjs:6709:15

 i don't see this error every time

 18:24:05.866 Given tab is not restoring. SessionStore.sys.mjs:6709:15
 _resetLocalTabRestoringState resource:///modules/sessionstore/SessionStore.sys.mjs:6709
 _restoreTabContentComplete resource:///modules/sessionstore/SessionStore.sys.mjs:7169
 _restoreTabContent resource:///modules/sessionstore/SessionStore.sys.mjs:7047
 removeTabs chrome://browser/content/tabbrowser/tabbrowser.js:4236
 TMP_closeAllTabs chrome://tabmixplus/content/minit/tablib.js:1166
 TMP_doCommand chrome://tabmixplus/content/click/click.js:218
 TMP_clickAction chrome://tabmixplus/content/click/click.js:173
 TMP_onTabClick chrome://tabmixplus/content/click/click.js:129
 handleEvent chrome://tabmixplus/content/tab/tab.js:475

 `_resetLocalTabRestoringState(aTab) {
 let browser = aTab.linkedBrowser;

 // Keep the tab's previous state for later in this method
 let previousState = TAB_STATE_FOR_BROWSER.get(browser);

 if (!previousState) {
   console.error("Given tab is not restoring.");
   return;
 }
 ......
`

 `_resetTabRestoringState(tab) {
   let browser = tab.linkedBrowser;

   if (!TAB_STATE_FOR_BROWSER.has(browser)) {
     console.error("Given tab is not restoring.");
     return;
   }

   this._resetLocalTabRestoringState(tab);
 },`

 check if tab mix try to reset restoring state for the wrong tabs
 check firefox code `close to right` `close to left`
--------------------------------------------------------------------------------------------------
2024-11-23
 there is a bug in our changecode function when we don't use toCode.
 the check for unmatched regexp does not run
--------------------------------------------------------------------------------------------------
2024-11-19
 - Waterfox 6.5.0 - on reload tab every - numbers are not visible on drop down
--------------------------------------------------------------------------------------------------
2024-11-16
 - check if we need to use "browser.tabs.insertAfterCurrentExceptPinned"
--------------------------------------------------------------------------------------------------
2024-11-13
 tab group related issues:
  - check if i need update after Bug 1920922
   Bug 1920922 - Update hidden tab checks to account for tabs in collapsed groups

  - check using browser.tabs.groups.enabled
  - check https://bugzilla.mozilla.org/show_bug.cgi?id=1925312
  maybe it is related to https://github.com/onemen/TabMixPlus/issues/352

  - need to add style color to tab group, to my custom colors in the profile userchrome.css
  maybe also to tab mix custom colors

 Bug 1908422: Add closedGroups to SessionRestore for tab groups
  - check if i need to change anything in our code for closed tab
  - check if i need UI for groups and closed groups

  tab group tabs are in <tab-group> container, update related styles
  look in chrome/browser/content/browser/tabbrowser/tabgroup.js
  and check firefox styles for it

  need to fix all places that use tabbrowser-arrowscrollbox > .tabbrowser-tab

  - check how tab-group work with dragging tabs is one row and multi-rows
    how dragging group work?

  - check if remove all tab and remove all tabs but also removes tab in hidden groups
--------------------------------------------------------------------------------------------------
2024-11-12
 tab/tabbar context menu
 - add unload tab to tabmix menu options
 - add tab group related menu??? (hidden by default)
 - find a why to add notification when firefox add/remove context menu items
 - check tabbar context menu - without enabling the tab context menu
 - maybe i can add options to control tabbar context menu, and add some custom menu item
--------------------------------------------------------------------------------------------------
2024-11-10
 - check how to apply prettify type
   youtube video from matt pocock https://www.youtube.com/watch?v=jEeQC6I8nlY&pp=ygULbWF0dCBwb2NvY2s%3D

   type Prettify<T> = {
     [K in keyof T]: T[K];
   } & {};

--------------------------------------------------------------------------------------------------
2024-11-08
check that i am using Promise.withResolvers the right way in MergeWindows.sys.mjs
--------------------------------------------------------------------------------------------------
2024-11-07
 check if i can use grid last-row to style tab width or max-width when tabwidthfittitle is off
 https://www.youtube.com/watch?v=HvhSEsFEsAg
--------------------------------------------------------------------------------------------------
2024-11-03
check this commit, maybe i need to update for full-screen
chore: followup bug 1927111 - Don't rely on static position (top: auto) for urlbar position
maybe i only need to call gURLBar.onWidgetAfterDOMChange when user is on multirow
if (Tabmix.isVersion(1340)) {
  gURLBar.onWidgetAfterDOMChange(gURLBar.textbox.parentNode);
}

Make urlbar a popover so that it draws on the top layer
https://bugzilla.mozilla.org/show_bug.cgi?id=1921811

The address bar was shown in full screen mode and was shown higher than normal after exiting full screen
https://bugzilla.mozilla.org/show_bug.cgi?id=1928255

--------------------------------------------------------------------------------------------------
2024-11-01
 check if i can load tabmix stylesheet one time in ScriptsLoader._loadCSS instead for each window
 see if i can move the function that load dynamic css to ScriptsLoader._loadCSS
--------------------------------------------------------------------------------------------------
2024-10-24
 - use rule that check for .sys.mjs only from readFromZip remove it from our eslint.config.js
 - add to readFromZip check that all .sys.mjs and .jsm files that Tabmix use exist in nightly
--------------------------------------------------------------------------------------------------
2024-10-18
- inject style to scrollbox shadow from css file, see this empale from autoReload.js
`
const linkElem = document.createElement("link");
linkElem.setAttribute("rel", "stylesheet");
linkElem.setAttribute("href", "chrome://tabmixplus/skin/preferences.css");
const dialog = document.getElementById("reloadevery_custom_dialog");
dialog.shadowRoot.appendChild(linkElem);
`
--------------------------------------------------------------------------------------------------
2024-10-05
 - check if i can use eslint-plugin-jsdoc
--------------------------------------------------------------------------------------------------
2024-10-01
maybe instead of disallow new tab button after tabs, set placeholder for the tab on the right (visibility: hidden;),
to keep its width but show the button after tabs.
this will prevent the button to be missing on the last row with one tab, i will have to update the calculation for the width
of the tabbar when both buttons take space
--------------------------------------------------------------------------------------------------
2024-09-26
 X check if i can add animation to multi-row height change
 it does not look good !!!
--------------------------------------------------------------------------------------------------
2024-09-18
check if i can use TabAttrModified to change tab style state
--------------------------------------------------------------------------------------------------
2024-09-15
add option to sort tabs
--------------------------------------------------------------------------------------------------
2024-09-10
  - check if i can load our stylesheets faster
  - maybe i can run TabmixTabbar.updateSettings and Tabmix.setTabStyle early
--------------------------------------------------------------------------------------------------
2024-09-09
 refactor Log.jsm to use console module
 see this example:
ChromeUtils.defineLazyGetter(lazy, "log", () => {
   let { ConsoleAPI } = ChromeUtils.importESModule(
     "resource://gre/modules/Console.sys.mjs"
   );
   let consoleOptions = {
      maxLogLevel: "all",
      prefix: "TabMix",
   };
   return new ConsoleAPI(consoleOptions);
 });

 Disallows Cu.reportError. This has been deprecated and should be replaced by
 console.error. // check new eslint-plugin-mozilla rule

 replace all log.jsm with console ??
--------------------------------------------------------------------------------------------------
2024-09-05
Key key_restoreLastClosedTabOrWindowOrSession of menuitem Reopen Closed Tab could not be found
maybe in need to clone key_restoreLastClosedTabOrWindowOrSession instead of replacing its id
document.getElementById("key_restoreLastClosedTabOrWindowOrSession").id = "key_undoCloseTab";
I saw in only one time
--------------------------------------------------------------------------------------------------
2024-08-28
check why our Tabmix.tablib._loadURI does not run for every changes in the url
--------------------------------------------------------------------------------------------------
2024-08-25
check how to convert our content script to use actors - https://firefox-source-docs.mozilla.org/dom/ipc/jsactors.html
check code in BrowserGlue.sys.mjs
check ClickHandlerChild.sys.mjs

- check possibility to add click listener directly to the browser
   gBrowser.selectedBrowser.ownerDocument.addEventListener("click", e=>console.log(e))
- use our code in tablib to prevent loading in current tab

- try to modify ClickHandlerParent and see it i can controll how it work
 --------------------------------------------------------------------------------------------------
2024-09-16
check if i can use Puppeteer for firefox-changes
--------------------------------------------------------------------------------------------------
2024-08-12
TMP_undocloseTabButtonObserver only work when the button is next to tabs toolbar
--------------------------------------------------------------------------------------------------
2024-08-11
try to reduce unnecessary work in Tabmix.setTabStyle, maybe i can use mutation observer on selected / visited
check if i can use MutationObserver, with attributeFilter and attributeOldValue ?
--------------------------------------------------------------------------------------------------
2024-07-22
add eslint rule to find unused methods from objects or class
--------------------------------------------------------------------------------------------------
2024-07-19
check how to select range of tab should work, maybe i need to update my tabclicking options?
check how every command work with single selected tab and multiple selected tab
--------------------------------------------------------------------------------------------------
2024-07-17
add to firefox-changes ALL firefox functions that are in modified/call in Tabmix
so we don't miss any change to functions name or variables
--------------------------------------------------------------------------------------------------
2024-07-16
i remove shortcut in SessionStore with comment
  `Bug 1689378 removed keyboard shortcut indicator for Firefox 87+`
- check if this was change again later see `browser\content\browser\customizableui\panelUI.js`
--------------------------------------------------------------------------------------------------
2024-07-10
check preference `toolkit.tabbox.switchByScrolling` and "DOMMouseScroll"
--------------------------------------------------------------------------------------------------
2024-07-07
undo close tabs issues:
 - "key_restoreLastClosedTabOrWindowOrSession" (Ctrl-Shit-T) - call History:RestoreLastClosedTabOrWindowOrSession -> restoreLastClosedTabOrWindowOrSession
   it does not restore closed tabs from other windows or sessions
 - "History:UndoCloseTab" - call undoCloseTab it restore tab(s)
   when undoCloseTab is called without any argument it only restored
   count = SessionStore.getLastClosedTabCount(window)
   but the menu is only disabled by:
   document.getElementById("context_undoCloseTab").disabled = SessionStore.getClosedTabCount() == 0;
--------------------------------------------------------------------------------------------------
2024-07-07:
protect all tabs
https://github.com/onemen/TabMixPlus/issues/294
protect selected tab(s) ?
freeze selected tab(s) ?
check if i use selectedtabs in more fetuses
--------------------------------------------------------------------------------------------------
Bug 1891797 - Close duplicate tabs from the context menu
Add preference to show/hide Close Duplicate Tabs
 - update the code to use 2 preference:
    1: hide build in Firefox preference - default to: ""
    2: show Tab mix preference - default to: (check current default)

    or: one preference that list menu items to show?

some menu items have build in pref: browser.tabs.context.close-duplicate.enabled
- this pref controle also the menu the allTabs menu button
--------------------------------------------------------------------------------------------------
new options:
  - remember opener for all tabs (after tab is no longer selected)
  - search in tab,closed tabs,closed tabs in closed windows
--------------------------------------------------------------------------------------------------
check what is the problem with:
https://github.com/onemen/TabMixPlus/discussions/292#discussioncomment-9238225
--------------------------------------------------------------------------------------------------
when opening multiple bookmarks in background tab title of the pendings tabs does not update
--------------------------------------------------------------------------------------------------
check background color for tab close button hover state.
need to calculate it based on current background color
--------------------------------------------------------------------------------------------------
 - fix github action changelog to replace <> with &lt; &gt;
 - fix it also in tabmix-build

 modify the workflow:
  - update the log on each build so dev-build page is up-to-date
    or make the dev-build in the doc dynamically get the info from github by using fetch?
  - check how to re-use the existing job for version release
--------------------------------------------------------------------------------------------------
 TabsToolbar not looking good on dark mode
--------------------------------------------------------------------------------------------------
check if in need to add keypress listener for ctrl-tab (lasttab)

check if this error caused by tabmixplus
buildList failed:  NS_ERROR_UNEXPECTED: WindowsJumpLists.sys.mjs:281:15
 buildList resource:///modules/WindowsJumpLists.sys.mjs:281
--------------------------------------------------------------------------------------------------
enable node corepack for testing
--------------------------------------------------------------------------------------------------
update gBrowser.warnAboutClosingTabs for waterfox G6
 need more testing for waterfox G6 when "browser.tabs.warnOnCloseOtherTabs" is false
 and "browser.tabs.warnOnClose" is true
--------------------------------------------------------------------------------------------------
 - for firefox 113+ check how to control min/max width with css var
 V when widthFitTitle is off and tabs are in overflow set max width the same as the min width
--------------------------------------------------------------------------------------------------
   Bug 1081542 - landed on 2024-03-01:
     - Ci.nsIBrowserDOMWindow.OPEN_NEWTAB_BACKGROUND
     - Ci.nsIBrowserDOMWindow.OPEN_NEWTAB_FOREGROUND

     After this change in _openURIInNewTab we only modify loadInBackground
     when aWhere is not one of the above values

   Backed out for causing Bug 1885050 on 2024-03-13

   external link open new tabs before Tab Mix is loaded, that is why it open new tab even if current tab is blank
--------------------------------------------------------------------------------------------------
reuse selected blank tab when opening new browser window from external app
 - test by opening github from vscode

Firefox, somehow, add the external link to the session and use restoreWindows

i still don't know how to check if the url is add from external link or from the restored session

maybe it is bug in SessionStore

check the report i filled in bugzilla
maybe i need to open new bug
check what changed in the code in restoreWindows
--------------------------------------------------------------------------------------------------
 Rename Tab Add-On for Firefox doesn't work in version 107.0.1 on PC.
  - https://mail.google.com/mail/u/0/#inbox/********************************
--------------------------------------------------------------------------------------------------
Disable scroll wheel tab switching over the edge
https://tabmixplus.org/forum/viewtopic.php?p=74120#p74120
--------------------------------------------------------------------------------------------------
  fix userChrome style for private window
  add option to style the color of the close button on tabs
--------------------------------------------------------------------------------------------------
Try to use tabnine when copilot is no longer free
https://www.tabnine.com/tabnine-vs-github-copilot
--------------------------------------------------------------------------------------------------
mac osx
- can not set some shortcut with Command + Alt
--------------------------------------------------------------------------------------------------
check TabUnloader.unloadLeastRecentlyUsedTab, gBrowser.discardBrowser
--------------------------------------------------------------------------------------------------
update session worker according to Bug 1761652
--------------------------------------------------------------------------------------------------
IOUtils not exist in Firefox 78, need a wrapper for it ?
--------------------------------------------------------------------------------------------------
fix webextension link and readme
--------------------------------------------------------------------------------------------------
add tabcontext menuitem option to show hide play tab/tabs
see bug 1684876 - Separate Play and Mute tab menu icons and functionality
--------------------------------------------------------------------------------------------------
task manager:
 https://clickup.com/?utm_medium=cpc&utm_campaign=task-management&utm_source=GetApp
--------------------------------------------------------------------------------------------------
make extensions for custom buttons
  - try to find scripts to add toolbar button to `open browser console`
--------------------------------------------------------------------------------------------------
  - check reddit,twitter and facebook to promot tabmix
  - check how to create discord chanel for tabmix
  - add links to twitter and facebook on github ?
--------------------------------------------------------------------------------------------------
set tabmix webextensions as incompatible with tabmixplus
 - tab mix plus - links
 - tab mix plus - rename tabs
--------------------------------------------------------------------------------------------------
add tab counter button
from Roy 2k: <EMAIL>
I use Tabcounter: https://github.com/WaldiPL/tabCounter
It's in the middle in between the address and the search bars.
--------------------------------------------------------------------------------------------------
  can not update extensions from github
  https://forum.eset.com/topic/23125-certificate-issues-for-firefox-740-64bit/

  need to change:
    extensions.update.requireBuiltInCerts: false
    extensions.install.requireBuiltInCerts: false
  to solve: Certificate issuer is not built-in
  need to check if it work in Firefox 78

  1632350226749	addons.xpi	WARN
  Download of https://github.com/onemen/TabMixPlus/releases/download/v1.0.0-pre.1/tab_mix_plus-dev-build.xpi failed: 302 Found

  _ERROR_ABORT: Certificate issuer is not built-in. CertUtils.jsm:183
  checkCert resource://gre/modules/CertUtils.jsm:183
  asyncOnChannelRedirect resource://gre/modules/CertUtils.jsm:211
  asyncOnChannelRedirect resource://gre/modules/addons/XPIInstall.jsm:2337
--------------------------------------------------------------------------------------------------
try to create windows installer for the helper scrips
--------------------------------------------------------------------------------------------------
  with scroll buttons on both sides sometimes the last tab on the right is marked as not visible
  so the scroll right button is always active
--------------------------------------------------------------------------------------------------
  try to use selenium for babelzilla
  babelzilla
  - fix lables for protectedtabs.closeWarning.5 and window.closeWarning.2 to match Firefox syntax
  V fix in minit.js : this.draglink = `Hold ${TabmixSvc.isMac ? "⌘" : "Ctrl"} to replace locked tab with link Url`;
  V check -ce.js files for lables
  V fix access key for tab context menu
  - prepare xpi for babelzilla
  - add all new labels to files in locale
  - check if babelzilla supports ftl
  - add locales that was removes from release back to source code,
    update our build to remove locales with low number of complete
    also update chrome.manifest
--------------------------------------------------------------------------------------------------
  style readme with svg - https://github.com/sindresorhus/css-in-readme-like-wat
  - add donation button to the readme
  ```

  https://github.com/patharanordev/donate-in-git

  [![Donate](https://img.shields.io/badge/Donate-PayPal-green.svg)](YOUR_EMAIL_CODE)
  ```
--------------------------------------------------------------------------------------------------
  -- format rename tab dialog with proton style, check how it work on RTL
  -- refactore rename panel to look line bookmark panel ?! with page image
--------------------------------------------------------------------------------------------------
  check that recently used tabs is work properly - TMP_LastTab.attachTab
--------------------------------------------------------------------------------------------------
  check Bug 1561435 - Do a formatting pass on mozilla-central using Prettier (the actual change)
  for Prettier configuration
--------------------------------------------------------------------------------------------------
  check if it possible to use position: fixed on build-in scroll button to place both of the buttons in our position
  with the spacer, or use clone
--------------------------------------------------------------------------------------------------
  check if it possible to load some scripts only when it needed
  check if it possible to load Tabmix code earlier
--------------------------------------------------------------------------------------------------
  check incompatible panel in Tabmix options
--------------------------------------------------------------------------------------------------
  check if i can use chrome/toolkit/content/global/preferencesBindings.js
--------------------------------------------------------------------------------------------------
 need to automate updating SCROLL BUTTON STYLE:
 as of 2021-07-25 the styles are up-to-date
 - try add <html:link rel="stylesheet" href="chrome://tabmixplus/skin/scrollbuttons.css"/>
    to scroll.js markup
    also possible is insert style from string link in chrome\toolkit\content\global\elements\menupopup.js
    <html:style>${this.styles}</html:style>

 - check all styles that i copied from skin/browser.css for mac and linux

 - make test folder with diff for Firefox 78,86,89,90,91
    Firefox 91
    need to fix our scroll buttons on non-proton style, test for backwared compatibility
    Bug 1714462 Remove most CSS rules using `not (-moz-proton)
    need to check also For Firefox 90
--------------------------------------------------------------------------------------------------
  for tabs title check if i can use mutation observer on label attribute
--------------------------------------------------------------------------------------------------
  check if i should disable auto reload on about: pages
--------------------------------------------------------------------------------------------------
  remove compatibility code with old extensions ????/
--------------------------------------------------------------------------------------------------
  fix webextension rename tab - fix for firefox 91
  - error in rename tab when detaching pending tabs
  fix in for webextension Tabmix - Links, fix for clicking on github
    clicking on commit id on the right should open in new tab (tried it on this page:
--------------------------------------------------------------------------------------------------
  check aboutNewTab actors BrowserTabChild.jsm
--------------------------------------------------------------------------------------------------
  add option to toggle compact mode
--------------------------------------------------------------------------------------------------
  CHECK IF WE NEED TO DO THIS TO PANELS - for our buttons on TabsToolbar
    - fix the code for button - it need to start only if the button is visible
    - don't start the template if the button not exist - do the same as allTabs-button - call init from the button before load
    - clean when it is drop and trigger init when it on the tool bar
    - need check if it can work on overflow menu
--------------------------------------------------------------------------------------------------
  need to review the code in firefox and update onTabBarScroll
  it look like everything is working but check for multi-row with orient horizontal
--------------------------------------------------------------------------------------------------
  test if Tabmix.restoreTabState work for pending tabs when
    V browser.sessionstore.restore_tabs_lazily is true
    - aTab.__SS_lazyData not in use anymore it was replaced with TAB_LAZY_STATES WeakMap
      there is only one place in session.js, no need to change it now
--------------------------------------------------------------------------------------------------
  It look like we can't use Fluent, need to use babelzilla
  add new labels to ftl file (Fluent) use this regexp: (label|tooltiptext)="(?!&).*"
  Migrating Legacy Strings to Fluent: https://firefox-source-docs.mozilla.org/main/68.0/intl/l10n/l10n/fluent_migrations.html
--------------------------------------------------------------------------------------------------
  I no longer see these issues:
  1. I no longer see this issue after Call addDynamicRules before first browser paint
    when starting with multi-row with 2-3 the tabbar height increase then shrink again

  2. https://github.com/onemen/TabMixPlus/issues/36
    new tab button after last tab not working
    when starting in multi-row mode with 2-3 tabs, new tab button is at the right on start
    when the pref is after last tab

  3. Tabmix not loading when "browser.uiCustomization.state" preference is messed-up
    - it don't see this anymore, need to check again after 2021-07-04
--------------------------------------------------------------------------------------------------
