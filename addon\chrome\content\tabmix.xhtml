<?xml version="1.0"?>

<?xul-overlay href="chrome://tabmixplus/content/overlay/tabContextMenu.xhtml"?>

<!DOCTYPE overlay [
<!ENTITY % tabmixDTD SYSTEM "chrome://tabmixplus/locale/tabmix.dtd" >
%tabmixDTD;
]>

<!-- original code from TBP Lite //-->

<overlay id="tabmix-overlay"
         xmlns:html="http://www.w3.org/1999/xhtml"
         xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul">

  <script type="text/javascript">
    // with bootstrap.js we get here after DOM<PERSON><PERSON><PERSON><PERSON>oa<PERSON> was fired
    // we can call our initializer with 'onContentLoaded' flag
    Tabmix.initialization.init.initialized = false;
    TMP_eventListener.init();
    Tabmix.initialization.run("onContentLoaded", gBrowser.tabContainer);
  </script>

   <!-- Menu in Tools Menu -->
   <menupopup id="menu_ToolsPopup">
      <menuitem id="tabmix-menu" label="&page.header.title;…" insertbefore="prefSep" tmp_iconic="menuitem-iconic tabmix-menu-icon"/>
      <menu id="tabmix-historyUndoWindowMenu"
            insertbefore="prefSep"
            label="&closedWin.label;"
            observes="tmp_closedwindows"
            tmp_iconic="menu-iconic closedwindows-icon">
         <menupopup id="tabmix-historyUndoWindowPopup"
            placespopup="true"/>
      </menu>
   </menupopup>

    <popup id="contentAreaContextMenu">
      <menuitem id="context-openlinkincurrent" label="&linkhere.label;" accesskey="&linkhere.accesskey;"/>
      <menuitem id="tm-openinverselink" label="" fglabel="&linkForegroundTab.label;" bglabel="&linkBackgroundTab.label;"
         fgaccesskey="&linkForegroundTab.accesskey;" bgaccesskey="&linkBackgroundTab.accesskey;"
         insertafter="context-openlinkintab"/>
      <menuitem id="tm-openAllLinks" label="&openalllinks.label;" accesskey="&openalllinks.accesskey;"
         insertafter="context-openlinkintab"/>
      <menuitem id="tm-linkWithhistory" label="&linkwithhistory.label;" accesskey="&linkwithhistory.accesskey;"
         insertafter="context-openlinkintab"/>
      <menuseparator id="tm-content-miscSep" insertafter="context-sep-viewbgimage,context-sep-open"/>
      <menuitem id="tm-content-closetab" data-lazy-l10n-id="tab-context-close-n-tabs" data-l10n-args='{"tabCount": 1}' insertbefore="context-sep-viewbgimage,context-sep-open"/>
      <menuitem id="tm-duplicateTabContext" key="key_tm_dupTab" label="&duplicateTabMenu.label;" accesskey="&duplicateTabMenu.accesskey;"
         insertbefore="context-sep-viewbgimage,context-sep-open"/>
      <menuitem id="tm-duplicateinWinContext" label="&duplicateinWin.label;" accesskey="&duplicateinWin.accesskey;"
         insertbefore="context-sep-viewbgimage,context-sep-open"/>
      <menuitem id="tm-detachTabContext" key="key_tm_detachTab" label="&detachTab.label;" accesskey="&detachTab.accesskey;"
         insertbefore="context-sep-viewbgimage,context-sep-open"/>
      <menuitem id="tm-mergeWindows" key="key_tm_mergeWin" label="&mergeContext.label;" accesskey="&mergeContext.accesskey;" insertbefore="context-sep-viewbgimage,context-sep-open" />
      <menuitem id="tm-content-freezeTab" type="checkbox"
         insertbefore="context-sep-viewbgimage,context-sep-open" label="&freezeTabMenu.label;" accesskey="&freezeTabMenu.accesskey;"/>
      <menuitem id="tm-content-protectTab" type="checkbox"
         insertbefore="context-sep-viewbgimage,context-sep-open" label="&protectTabMenu.label;" accesskey="&protectTabMenu.accesskey;"/>
      <menuitem id="tm-content-lockTab" type="checkbox"
         insertbefore="context-sep-viewbgimage,context-sep-open" label="&lockTabMenu.label;" accesskey="&lockTabMenu.accesskey;"/>
      <menu id="tm-tabsList" label="&tabsList.label;" accesskey="&tabsList.accesskey;" insertbefore="context-sep-viewbgimage,context-sep-open" tmp_iconic="menu-iconic tabmix-tabslist-icon">
        <menupopup id="tm-tabsList-menu"/>
      </menu>
      <menuseparator id="tm-content-undoCloseSep" insertbefore="context-sep-viewbgimage,context-sep-open"/>
      <menuseparator id="tm-content-after-undoCloseList" insertbefore="context-sep-viewbgimage,context-sep-open"/>
      <menuitem id="tm-content-undoCloseTab" data-lazy-l10n-id="tab-context-reopen-closed-tabs" data-l10n-args='{"tabCount": 1}'
         command="History:UndoCloseTab" insertbefore="tm-content-after-undoCloseList"
         key="key_restoreLastClosedTabOrWindowOrSession"/>
      <menu id="tm-content-undoCloseList" label="&undoCloseListMenu.label;" accesskey="&undoCloseListMenu.accesskey;" tmp_iconic="menu-iconic closedtabs-icon"
         insertbefore="tm-content-after-undoCloseList">
        <menupopup id="tm-content-undoCloseList-menu"/>
      </menu>
      <menu id="tm-autoreload_menu" insertafter="context-sep-navigation"
            labelTab="&autoReloadTab.label;" accesskeyTab="&autoReloadTab.accesskey;"
            labelSite="&autoReloadSite.label;" accesskeySite="&autoReloadSite.accesskey;">
        <menupopup data-popup="autoReload"/>
      </menu>
    </popup>

    <html:template id="tabmix-closedWindows-container">
      <panelview id="tabmix-closedWindowsView" class="PanelUI-subView tabmix-subview"/>
    </html:template>

    <html:template id="tabmix-closedTabs-container">
      <panelview id="tabmix-closedTabsView" class="PanelUI-subView tabmix-subview"/>
    </html:template>

    <toolbarbutton id="allTabsMenu_sortTabsButton"
      class="subviewbutton"
      closemenu="none"
      context=""
      _oncommand="Tabmix.allTabs.sortTabsInList();"
      label="&sortedTabs.label;"
      insertbefore="allTabsMenu-groupsSeparator,allTabsMenu-tabsSeparator"
      type="checkbox"/>

    <!-- add auto reload popup to reload button -->
    <toolbarbutton id="reload-button">
      <menupopup data-popup="autoReload"/>
    </toolbarbutton>


    <popupset id="mainPopupSet">
      <!-- popup menu for autoreload -->
      <menupopup id="autoreload_popup" data-popup="autoReload">
        <menuitem label="&autoReloadTab.label;"  disabled="true" style="font-weight: bold;color: #CC0000;"/>
        <menuseparator />
        <menuitem _label="&enable.label;:" type="checkbox"
                  seconds="&seconds.label;" minute="&minute.label;" minutes="&minutes.label;"
                  data-command="toggle"/>
        <menuseparator />
        <menuitem label="&custom.label;…" anonid="start_custom_list"
                  data-command="customTime"/>
        <menuseparator anonid="end_custom_list"/>
        <menuitem label="1  &minute.label;"  type="radio" value="60"/>
        <menuitem label="2  &minutes.label;" type="radio" value="120"/>
        <menuitem label="5  &minutes.label;" type="radio" value="300"/>
        <menuitem label="15 &minutes.label;" type="radio" value="900"/>
        <menuitem label="30 &minutes.label;" type="radio" value="1800"/>
        <menuseparator anonid="end_custom_list"/>
        <menuitem label="&enableTabs.label;" data-command="enableAllTabs"/>
        <menuitem label="&disableTabs.label;" data-command="disableAllTabs"/>
      </menupopup>
      <!-- context menu for undoclosetab menupopup -->
      <menupopup id="tm_undocloseContextMenu">
        <menuitem id="tmOpen" label="&restoreincurrent.label;" accesskey="&restoreincurrent.accesskey;"
                  commandData="restoreTab,current"/>
        <menuitem id="tmOpenInNewWindow" label="&restoreinwin.label;" accesskey="&restoreinwin.accesskey;"
                  commandData="restoreTab,window"/>
        <menuitem id="tmOpenInNewTab" label="&restoreintab.label;" accesskey="&restoreintab.accesskey;"
                  commandData="restoreTab,tab"/>
        <menuitem id="tmRestoreTab" label="&restoretab.label;" accesskey="&restoretab.accesskey;"
                  commandData="restoreTab,original" default="true"/>
        <menuseparator id="tmpre-bookmarks-separator"/>
        <menuitem id="tmAddBookmark" label="&bookmark.label;" accesskey="&bookmark.accesskey;"
                  commandData="addBookmarks"/>
        <menuitem id="tmcopyTabUrl" label="&copytaburl.label;" accesskey="&copytaburl.accesskey;"
                  commandData="copyTabUrl"/>
        <menuseparator id="tmpost-bookmarks-separator"/>
        <menuitem id="tm_delete" label="&deletelist.label;" accesskey="&deletelist.accesskey;"
                  commandData="restoreTab,delete"/>
      </menupopup>
      <!-- context menu for undoclosewindow menupopup for use with sessionRestore -->
      <menupopup id="tm_undocloseWindowContextMenu">
        <menuitem id="tm_delete-window" label="&deletelist.label;" accesskey="&deletelist.accesskey;"/>
      </menupopup>
      <!-- menupopup for lasttab -->
      <menupopup id="lasttabTabList" ignorekeys="true" flex="1"/>
      <menupopup id="tabslist"/>
      <!-- popup for show\hide the tabbar -->
      <html:template id="tabmix_hideTabbar_menu-container">
        <menuseparator id="tabmix_hideTabbar_separator" tabmix_context="true"/>
        <menu id="tabmix_hideTabbar_menu" tabmix_context="true" label="&hideTabBar.label;" accesskey="&hideTabBar.label.key;">
          <menupopup id="tabmix_hideTabbar_popup">
            <menuitem value="0" type="radio" label="&hideTabBar.never.label;" accesskey="&hideTabBar.never.key;"/>
            <menuitem value="1" type="radio" label="&hideTabBar.oneTab.label;" accesskey="&hideTabBar.onOneTab.key;"/>
            <menuitem value="2" type="radio" label="&hideTabBar.always.label;" accesskey="&hideTabBar.always.key;"/>
          </menupopup>
        </menu>
      </html:template>
      <!-- tabmix tooltip for button -->
      <tooltip id="tabmix-tooltip"/>
      <!-- tabmix tooltip for tabmix-scrollbox -->
      <tooltip id="tabmix-rows-tooltip" observes="tabmix_flowing"/>
    </popupset>

    <toolbar id="TabsToolbar-customization-target">
      <tabmixscrollbox id="tabmix-scrollbox" insertafter="tabbrowser-tabs"
                     observes="tabmix_flowing"
                     tooltip="tabmix-rows-tooltip"
                     cui-areatype="toolbar"
                     removable="false"/>
    </toolbar>

  <!-- Tabmix Plus broadcasterset -->
  <broadcasterset id="mainBroadcasterSet" insertafter="mainKeyset">
    <broadcaster id="tmp_closedwindows"/>
    <broadcaster id="tmp_undocloseButton"/>
    <broadcaster id="tmp_disableSave"/>
    <broadcaster id="tabmix_flowing"/>
  </broadcasterset>

</overlay>
