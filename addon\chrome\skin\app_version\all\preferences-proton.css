/**
    Firefox version 91+

    All platform

    proton styles for preferences.xhtml and child windows
**/

@namespace html url("http://www.w3.org/1999/xhtml");

:root {
  --shortcut-image-stroke: #fff;

  /* for autoReload.xhtml */
  --combined-element-border-radius: 4px;
  --combined-element-outline: 1px solid var(--in-content-focus-outline-color);
}

/* for Zen browser */
@media not (prefers-contrast) {
  :root:not([lwtheme]) {
    --color-accent-primary: light-dark(rgb(0 97 224), rgb(0 221 255)) !important;
    --in-content-button-background: color-mix(in srgb, currentcolor 7%, transparent) !important;
    --in-content-button-background-hover: color-mix(in srgb, currentcolor 14%, transparent) !important;
    --in-content-button-background-active: color-mix(in srgb, currentcolor 21%, transparent) !important;
    --in-content-primary-button-text-color: rgb(251 251 254) !important;
    --in-content-primary-button-text-color-hover: var(--in-content-primary-button-text-color) !important;
    --in-content-primary-button-text-color-active: var(--in-content-primary-button-text-color) !important;
    --in-content-primary-button-background: #0061e0 !important;
    --in-content-primary-button-background-hover: #0250bb !important;
    --in-content-primary-button-background-active: #053e94 !important;
  }
}

.paneSelector > radio {
  border-top-right-radius: 4px;
  border-top-left-radius: 4px;
}

.paneSelector > radio .radio-label {
  font-size: 1.25em;
  font-weight: 300;
}

/* tabpanels and tabs */

tabpanels {
  padding: 6px 10px;
}

tabs {
  margin: 0;
}

tabs.tabs-hidden {
  border-bottom: 1px transparent;
}

tab {
  padding: 2px 10px 0 !important;
  min-height: 24px;
}


html|input[type="color"],
button {
  min-height: 26px;
  padding: 4px 10px;
  margin: 2px 8px;
}

html|input[type="color"] {
  border: 1px solid var(--in-content-box-border-color);
  min-height: initial;
  padding: 4px;
  margin: 2px 4px;
}

@media (prefers-contrast) {
  button:not(.primary, [default], [type="submit"], [autofocus]),
  html|input[type="color"],
  menulist {
    border-color: ButtonBorder;
  }
}

html|fieldset {
 border-radius: 4px;
}

html|legend {
  font-weight: 600;
}

/* pref-filetype */
.filetype-content {
  border: none;
}

#filetypeEntry {
  padding-block: 4px;
}

.filetype-buttons,
.filetype-content > hbox {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filetype-content > hbox {
  margin-top: 10px;
}

.filetype-content > hbox > *:first-child {
  margin-inline-start: 0;
}

.filetype-content > hbox > *:last-child {
  margin-inline-end: 0;
}

.filetype-separator {
  border-top: 1px solid ThreeDShadow;
  border-bottom: 1px solid ThreeDHighlight;
  height: 0 !important;
  margin: 0.4em 0.4em 1em;
}

.filetype-buttons {
  margin-inline: 11px;
}

/* override rules from common.css  */
checkbox_tmp .checkbox-check {
	margin-block: 0;
}

menulist {
  border: 0;
  padding-block: 4px;
  padding-inline: 12px 8px;
  margin: 5px 2px 3px;
  min-height: 23.2px;
}

/* override rules from app_version/all/prefwindow.css */
#TabMIxPreferences .paneSelector {
  background-color: inherit;
  color: inherit;
}

radio[pane]:hover {
  background-color: var(--in-content-button-background-hover);
 }

radio[pane][selected="true"] {
  background-color: var(--in-content-button-background);
  color: var(--in-content-accent-color);
 }

 radio[pane]:hover:active,
 radio[pane][selected="true"]:hover {
  background-color: var(--in-content-button-background-active);
 }

/* override rules from skin/tabmix-preferences.css */
#TabMIxPreferences > .donate-button-container {
  margin-top: -6px;
}

prefpane {
  padding: 3px 0 1px;
}

prefpane > .content-box > tabbox {
  height: unset;
  min-height: var(--content-box-max-pane-height, 505px);
}

#TabMIxPreferences[mac] prefpane > .content-box > tabbox {
  min-height: var(--content-box-max-pane-height, 505px);
}

#TabMIxPreferences[linux] prefpane > .content-box > tabbox {
  width: 560px;
  min-height: var(--content-box-max-pane-height, 540px);
}

.prefWindow-dlgbuttons {
	padding-inline-start: 10px;
}

#TabMIxPreferences:not([linux], [mac]) .groupbox-tabbox:not(#mouseclick_tabbox) > tabs {
  border-bottom: none;
	margin-bottom: 4px;
}

#TabMIxPreferences:not([ubuntu]) .groupbox-panels:not(.tabclick) {
  appearance: none;
  border: 2px groove ThreeDFace;
  border-radius: 4px;
  padding-inline: 0.75em;
}

/*  tabclicking options tabbox  */
.mouseclick-panel {
  --mouseclick-border: 1px solid color-mix(in srgb, var(--in-content-accent-color) 41%, transparent);

  display: block;
  margin-top: 25px;
  margin-inline: 25px;
}

#mouseclick_tabbox {
 border: 2px solid var(--in-content-border-color);
 border-radius: 4px;
 padding: 0;
}

#mouseclick_tabbox > #tabclick {
 display: flex;
 margin: 0;
}

#tabclick > tab {
  padding: 2px 0 0 !important;
  flex: 1 auto;
}

#tabclick .subtabs .tab-text {
	min-width: initial;
}

#mouseclick_tabbox > .groupbox-panels menulist {
  margin-inline-end: 23px;
}
